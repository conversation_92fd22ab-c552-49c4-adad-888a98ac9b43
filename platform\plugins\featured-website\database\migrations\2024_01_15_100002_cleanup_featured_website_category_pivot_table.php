<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Drop the table if it exists (cleanup from failed migration)
        Schema::dropIfExists('featured_website_category_featured_website');
        
        // Recreate it properly
        Schema::create('featured_website_category_featured_website', function (Blueprint $table) {
            $table->foreignId('category_id')->index('fw_cat_pivot_cat_id_index');
            $table->foreignId('featured_website_id')->index('fw_cat_pivot_fw_id_index');
            $table->primary(['category_id', 'featured_website_id'], 'fw_cat_pivot_primary');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('featured_website_category_featured_website');
    }
};
