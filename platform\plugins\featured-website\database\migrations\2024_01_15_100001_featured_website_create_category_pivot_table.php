<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('featured_website_category_featured_website', function (Blueprint $table) {
            $table->foreignId('category_id')->index();
            $table->foreignId('featured_website_id')->index();
            $table->primary(['category_id', 'featured_website_id'], 'featured_website_category_pivot_primary');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('featured_website_category_featured_website');
    }
};
