<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Shaqi\FeaturedWebsite\Models\FeaturedWebsite;
use <PERSON>haqi\FeaturedWebsite\Models\FeaturedWebsiteCategory;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Define the mapping of bad names to good names
        $categoryMappings = [
            '["Landing Page"]' => 'Landing Page',
            '["Optimal"]' => 'Optimal',
            '["The Vitals"]' => 'The Vitals',
            '["Property Website"]' => 'Property Website',
        ];
        
        foreach ($categoryMappings as $badName => $goodName) {
            // Find the bad category (with brackets and quotes)
            $badCategory = FeaturedWebsiteCategory::where('name', $badName)->first();
            
            // Find the good category (clean name)
            $goodCategory = FeaturedWebsiteCategory::where('name', $goodName)->first();
            
            if ($badCategory && $goodCategory) {
                echo "Moving websites from '{$badName}' to '{$goodName}'\n";
                
                // Get all websites associated with the bad category
                $websites = $badCategory->featuredWebsites;
                
                // Associate them with the good category
                foreach ($websites as $website) {
                    $website->categories()->syncWithoutDetaching([$goodCategory->id]);
                    echo "  - Moved website: {$website->name}\n";
                }
                
                // Remove associations with bad category
                $badCategory->featuredWebsites()->detach();
                
                // Delete the bad category
                $badCategory->delete();
                echo "  - Deleted bad category: '{$badName}'\n";
                
            } elseif ($badCategory && !$goodCategory) {
                // If only bad category exists, rename it to good name
                echo "Renaming '{$badName}' to '{$goodName}'\n";
                $badCategory->name = $goodName;
                $badCategory->save();
            }
        }
        
        echo "Cleanup completed successfully!\n";
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This cleanup is irreversible, but we can log what we're doing
        echo "Cannot reverse cleanup migration - changes are permanent\n";
    }
};
