<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('featured_website_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name', 255);
            $table->string('status', 60)->default('published');
            $table->timestamps();
        });

        Schema::create('featured_website_categories_translations', function (Blueprint $table) {
            $table->string('lang_code');
            $table->integer('featured_website_categories_id');
            $table->string('name', 255)->nullable();

            $table->primary(['lang_code', 'featured_website_categories_id'], 'featured_website_categories_translations_primary');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('featured_website_categories');
        Schema::dropIfExists('featured_website_categories_translations');
    }
};
