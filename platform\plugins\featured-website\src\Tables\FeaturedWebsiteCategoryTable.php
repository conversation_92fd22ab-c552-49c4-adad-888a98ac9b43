<?php

namespace Shaqi\FeaturedWebsite\Tables;

use Illuminate\Support\Facades\Auth;
use Shaqi\Base\Enums\BaseStatusEnum;
use <PERSON>haqi\Base\Facades\BaseHelper;
use <PERSON><PERSON>qi\Base\Facades\Html;
use <PERSON><PERSON>qi\FeaturedWebsite\Repositories\Interfaces\FeaturedWebsiteCategoryInterface;
use Shaqi\Table\Abstracts\TableAbstract;
use Illuminate\Contracts\Routing\UrlGenerator;
use Yajra\DataTables\DataTables;
use Illuminate\Http\JsonResponse;

class FeaturedWebsiteCategoryTable extends TableAbstract
{

    protected $hasActions = true;

    protected $hasFilter = true;

    public function __construct(DataTables $table, UrlGenerator $urlGenerator, FeaturedWebsiteCategoryInterface $featuredWebsiteCategoryRepository)
    {
        parent::__construct($table, $urlGenerator);

        $this->repository = $featuredWebsiteCategoryRepository;

        if (! Auth::user()->hasAnyPermission(['featured-website-category.edit', 'featured-website-category.destroy'])) {
            $this->hasOperations = false;
            $this->hasActions = false;
        }
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function ($item) {
                if (! Auth::user()->hasPermission('featured-website-category.edit')) {
                    return $item->name;
                }
                return Html::link(route('featured-website-category.edit', $item->id), $item->name);
            })
            ->editColumn('checkbox', function ($item) {
                return $this->getCheckbox($item->id);
            })
            ->editColumn('created_at', function ($item) {
                return BaseHelper::formatDate($item->created_at);
            })
            ->editColumn('status', function ($item) {
                return $item->status->toHtml();
            })
            ->addColumn('operations', function ($item) {
                return $this->getOperations('featured-website-category.edit', 'featured-website-category.destroy', $item);
            });

        return $this->toJson($data);
    }

    public function query()
    {
        $query = $this->repository->getModel()->select([
           'id',
           'name',
           'created_at',
           'status',
       ]);

        return $this->applyScopes($query);
    }

    public function columns()
    {
        return [
            'id' => [
                'title' => trans('core/base::tables.id'),
                'width' => '20px',
            ],
            'name' => [
                'title' => trans('core/base::tables.name'),
                'class' => 'text-start',
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'width' => '100px',
            ],
            'status' => [
                'title' => trans('core/base::tables.status'),
                'width' => '100px',
            ],
        ];
    }

    public function buttons()
    {
        return $this->addCreateButton(route('featured-website-category.create'), 'featured-website-category.create');
    }

    public function bulkActions(): array
    {
        return $this->addDeleteAction(route('featured-website-category.deletes'), 'featured-website-category.destroy', parent::bulkActions());
    }

    public function getBulkChanges(): array
    {
        return [
            'name' => [
                'title'    => trans('core/base::tables.name'),
                'type'     => 'text',
                'validate' => 'required|max:120',
            ],
            'status' => [
                'title'    => trans('core/base::tables.status'),
                'type'     => 'select',
                'choices'  => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type'  => 'date',
            ],
        ];
    }

    public function getFilters(): array
    {
        return $this->getBulkChanges();
    }
}
