{"__meta": {"id": "X4f3c48bb26fd5697d27f3b02d1479fa6", "datetime": "2025-07-31 12:05:41", "utime": 1753963541.469606, "method": "GET", "uri": "/admin/audit-logs/widgets/activities?_=1753963533645", "ip": "127.0.0.1"}, "php": {"version": "8.1.4", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963535.433512, "end": 1753963541.469636, "duration": 6.036123991012573, "duration_str": "6.04s", "measures": [{"label": "Booting", "start": 1753963535.433512, "relative_start": 0, "end": 1753963540.657709, "relative_end": 1753963540.657709, "duration": 5.224196910858154, "duration_str": "5.22s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753963540.658785, "relative_start": 5.225273132324219, "end": 1753963541.469639, "relative_end": 3.0994415283203125e-06, "duration": 0.8108539581298828, "duration_str": "811ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 42479752, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 10, "templates": [{"name": "plugins/audit-log::widgets.activities (\\platform\\plugins\\audit-log\\resources\\views\\widgets\\activities.blade.php)", "param_count": 2, "params": ["histories", "limit"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/widgets/activities.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "plugins/audit-log::activity-line (\\platform\\plugins\\audit-log\\resources\\views\\activity-line.blade.php)", "param_count": 8, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/audit-log/resources/views/activity-line.blade.php:0"}, {"name": "core/dashboard::partials.paginate (\\platform\\core\\dashboard\\resources\\views\\partials\\paginate.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "histories", "limit", "__currentLoopData", "history", "loop", "data"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/partials/paginate.blade.php:0"}]}, "route": {"uri": "GET admin/audit-logs/widgets/activities", "middleware": "web, core, auth", "as": "audit-log.widget.activities", "permission": "audit-log.index", "controller": "Shaqi\\AuditLog\\Http\\Controllers\\AuditLogController@getWidgetActivities", "namespace": "Shaqi\\AuditLog\\Http\\Controllers", "prefix": "admin/audit-logs", "where": [], "file": "<a href=\"vscode://file/D:\\laragon\\www\\focusedcre\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php:24\">\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php:24-41</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01104, "accumulated_duration_str": "11.04ms", "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 12}], "duration": 0.0078, "duration_str": "7.8ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "focusedcre", "start_percent": 0, "width_percent": 70.652}, {"sql": "select count(*) as aggregate from `audit_histories`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 35}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 70.652, "width_percent": 6.612}, {"sql": "select * from `audit_histories` order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 35}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 77.264, "width_percent": 9.964}, {"sql": "select * from `users` where `users`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 23, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 24, "namespace": null, "name": "\\platform\\plugins\\audit-log\\src\\Http\\Controllers\\AuditLogController.php", "line": 35}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 87.228, "width_percent": 12.772}]}, "models": {"data": {"Shaqi\\AuditLog\\Models\\AuditHistory": 8, "Shaqi\\ACL\\Models\\User": 2}, "count": 10}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"https://focusedcre.local/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "admin-theme": "default"}, "request": {"path_info": "/admin/audit-logs/widgets/activities", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-518047560 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">1753963533645</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518047560\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1939734465 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1939734465\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1138211535 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">https://focusedcre.local/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2696 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _gat_gtag_UA_156191303_1=1; _ga=GA1.1.1448235322.1747742727; XSRF-TOKEN=eyJpdiI6Im44REZVOXpRN0o1TGFHdGQ3S0xaZEE9PSIsInZhbHVlIjoiTXFYWnF2WGNCSWZqOGZpKzdPWGZUYnZ3UHpxYmZrbDUvUWkzUW1BeVI3dGxIKytLUFNqRWFkMEE0U3JEdUNUa2NlWHBtK0g5c0oycXd3VW9uNXg0eHF2RUVlSmhnbFNwcUlUV29la0xiZkQybFlpeW9yT1NINE1oZ3gva21ZckMiLCJtYWMiOiJkZmMzZDkzNWQ5NGY3ZjRmMGEyMzAxYjVhODdiMmNlZmNmMTRhMzVkZjJhMDYxMzQyOWJlZjdjOWU5OTlmNmRhIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IndBalBwaTFwQU55S2c5Z1RaTzVFTHc9PSIsInZhbHVlIjoiaDIwUXVyQ2VzMWFwNnZCNnNWZDQyWlppOFRpZnZ2bTB0OG5PWWhNOUw2TTVMMGZnK1dDdWx1d3RlbGhEL1F1Y2VFb2R6WEpTUHRxZjBybHY4TGl4eVZPc1dRZ2UvUXcxMERqT2pUcnZQd2RScnFkQ05Xa3FGL3V5dElhQWMvZFIiLCJtYWMiOiI5OTI5MjdmYjRjOTRkYTFhYWZkZTc3OTdlNTEyMWNhZWU1Y2MxZDBjMWNhODAwZTZhZDhkYjAyYzg3ODU5Y2Q0IiwidGFnIjoiIn0%3D; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138211535\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-985178394 data-indent-pad=\"  \"><span class=sf-dump-note>array:50</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"30 characters\">https://focusedcre.local/admin</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2696 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _gat_gtag_UA_156191303_1=1; _ga=GA1.1.1448235322.1747742727; XSRF-TOKEN=eyJpdiI6Im44REZVOXpRN0o1TGFHdGQ3S0xaZEE9PSIsInZhbHVlIjoiTXFYWnF2WGNCSWZqOGZpKzdPWGZUYnZ3UHpxYmZrbDUvUWkzUW1BeVI3dGxIKytLUFNqRWFkMEE0U3JEdUNUa2NlWHBtK0g5c0oycXd3VW9uNXg0eHF2RUVlSmhnbFNwcUlUV29la0xiZkQybFlpeW9yT1NINE1oZ3gva21ZckMiLCJtYWMiOiJkZmMzZDkzNWQ5NGY3ZjRmMGEyMzAxYjVhODdiMmNlZmNmMTRhMzVkZjJhMDYxMzQyOWJlZjdjOWU5OTlmNmRhIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IndBalBwaTFwQU55S2c5Z1RaTzVFTHc9PSIsInZhbHVlIjoiaDIwUXVyQ2VzMWFwNnZCNnNWZDQyWlppOFRpZnZ2bTB0OG5PWWhNOUw2TTVMMGZnK1dDdWx1d3RlbGhEL1F1Y2VFb2R6WEpTUHRxZjBybHY4TGl4eVZPc1dRZ2UvUXcxMERqT2pUcnZQd2RScnFkQ05Xa3FGL3V5dElhQWMvZFIiLCJtYWMiOiI5OTI5MjdmYjRjOTRkYTFhYWZkZTc3OTdlNTEyMWNhZWU1Y2MxZDBjMWNhODAwZTZhZDhkYjAyYzg3ODU5Y2Q0IiwidGFnIjoiIn0%3D; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1315 characters\">C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\ProgramData\\ComposerSetup\\bin;C:\\Program Files\\Java\\jdk-15.0.2;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Amazon\\AWSCLIV2\\;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\laragon\\bin\\composer;D:\\laragon\\bin\\git\\bin;D:\\laragon\\bin\\git\\cmd;D:\\laragon\\bin\\git\\mingw64\\bin;D:\\laragon\\bin\\git\\usr\\bin;D:\\laragon\\bin\\mongodb\\mongodb-4.0.3\\;D:\\laragon\\bin\\mysql\\mariadb-10.6.7-winx64\\bin;D:\\laragon\\bin\\ngrok;D:\\laragon\\bin\\nodejs\\node-v16.16.0;D:\\laragon\\bin\\php\\php-8.1.4-Win32-vs16-x64;D:\\laragon\\bin\\postgresql\\postgresql\\bin;D:\\laragon\\bin\\python\\python-3.13;D:\\laragon\\bin\\python\\python-3.13\\Scripts;D:\\laragon\\bin\\yarn\\bin;D:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.47 (Win64) OpenSSL/1.1.1m PHP/8.1.4</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">D:/laragon/www/focusedcre/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">23244</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"36 characters\">/admin/audit-logs/widgets/activities</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"15 characters\">_=1753963533645</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"15 characters\">_=1753963533645</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/admin/audit-logs/widgets/activities?_=1753963533645</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753963535.4335</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753963535</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985178394\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1207226780 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">276c8eb96ecbc99bdaa7c37095beb04735960841</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;footprint&quot;:&quot;276c8eb96ecbc99bdaa7c37095beb04735960841&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;focusedcre.local&quot;,&quot;landing_page&quot;:&quot;new-website-request&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|6LfPDjUyZLXD0DhNLxRR7WMt2NSfbdUnceBmo2YcfbIcDul5oc1icuy6Y0d2|$2y$10$v3mwe1ya6fDmDwB5vikHTuie26Jjsc.ZMD/k9JtQCyVybjxTgiKCu</span>\"\n  \"<span class=sf-dump-key>_gid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gat_gtag_UA_156191303_1</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">801BcPCBI6AAQOWuMaIoY4d3XHvjwOixQ6zrnhTO</span>\"\n  \"<span class=sf-dump-key>_ga_94P3KEDHFG</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207226780\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2009226900 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:05:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlJIWHc0azIzNWFKMHJlWGhIQjFlRnc9PSIsInZhbHVlIjoiQVFDVEdLN0pIWnVPWU5sVVNudGh3ckhpYzlFbFhDeVZhRHVBc3VvNkYyV1o4aEpCZXh1aUJQYkNid1FjSFFMWVIwWG1DTmtjOFdYRFVIOG52ZTJteFMySVh1dVRUQThPQXRDSW0zeFZjU0w2ekpPSDRYQVBpODI3RDNjUlZDaGQiLCJtYWMiOiIxNjRmOWVmNjk2NWQ5ZWY2MDQ5ZGE1ZjM2ODllMjY2MmU0MmM0NjhjY2VlZmE3YTNiZDE0MDZlNGU2YTZiNDE5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:05:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shaqi_session=eyJpdiI6IjlrcDB0aFVlNmVQMVdNRHU2V2lMZXc9PSIsInZhbHVlIjoiTzViYU5FdE95bWtTandENTM5b3BBelhQYXdxQzQvR3NvRitmN0g2RFJ4cGNNOUFlUkp0M3VWSWFpRlNFOEFqcmxGREU3Q2lENUgzSzh6cUFXOGpET21GRjJENTNXdEpJOEVRVTZ1aWZkVUxqMGgrTzV0V3pGKzdkNzM3Qkp4NFEiLCJtYWMiOiI1NTQ2NmE0NTkzMzc5NDgyMDU2NDVhZTBjODc5YTQxYTAxMDBlMGQ3ZjMyNmQ2ZDQyMTYwNzk3NzliNDIxNTQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:05:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlJIWHc0azIzNWFKMHJlWGhIQjFlRnc9PSIsInZhbHVlIjoiQVFDVEdLN0pIWnVPWU5sVVNudGh3ckhpYzlFbFhDeVZhRHVBc3VvNkYyV1o4aEpCZXh1aUJQYkNid1FjSFFMWVIwWG1DTmtjOFdYRFVIOG52ZTJteFMySVh1dVRUQThPQXRDSW0zeFZjU0w2ekpPSDRYQVBpODI3RDNjUlZDaGQiLCJtYWMiOiIxNjRmOWVmNjk2NWQ5ZWY2MDQ5ZGE1ZjM2ODllMjY2MmU0MmM0NjhjY2VlZmE3YTNiZDE0MDZlNGU2YTZiNDE5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:05:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">shaqi_session=eyJpdiI6IjlrcDB0aFVlNmVQMVdNRHU2V2lMZXc9PSIsInZhbHVlIjoiTzViYU5FdE95bWtTandENTM5b3BBelhQYXdxQzQvR3NvRitmN0g2RFJ4cGNNOUFlUkp0M3VWSWFpRlNFOEFqcmxGREU3Q2lENUgzSzh6cUFXOGpET21GRjJENTNXdEpJOEVRVTZ1aWZkVUxqMGgrTzV0V3pGKzdkNzM3Qkp4NFEiLCJtYWMiOiI1NTQ2NmE0NTkzMzc5NDgyMDU2NDVhZTBjODc5YTQxYTAxMDBlMGQ3ZjMyNmQ2ZDQyMTYwNzk3NzliNDIxNTQ1IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:05:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009226900\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1441649553 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">https://focusedcre.local/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin-theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441649553\", {\"maxDepth\":0})</script>\n"}}