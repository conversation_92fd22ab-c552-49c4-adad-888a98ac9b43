<?php

namespace Shaqi\FeaturedWebsite\Http\Controllers;

use Shaqi\Base\Events\BeforeEditContentEvent;
use Shaqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use <PERSON>haqi\Base\Events\UpdatedContentEvent;
use <PERSON>haqi\Base\Http\Controllers\BaseController;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\FeaturedWebsite\Forms\FeaturedWebsiteCategoryForm;
use Shaqi\Base\Forms\FormBuilder;
use Shaqi\FeaturedWebsite\Http\Requests\FeaturedWebsiteCategoryRequest;
use Shaqi\FeaturedWebsite\Repositories\Interfaces\FeaturedWebsiteCategoryInterface;
use Shaqi\FeaturedWebsite\Tables\FeaturedWebsiteCategoryTable;
use Exception;
use Illuminate\Http\Request;
use Shaqi\Base\Facades\PageTitle;

class FeaturedWebsiteCategoryController extends BaseController
{
    public function __construct(protected FeaturedWebsiteCategoryInterface $featuredWebsiteCategoryRepository)
    {
    }

    public function index(FeaturedWebsiteCategoryTable $table)
    {
        PageTitle::setTitle(trans('plugins/featured-website::featured-website-category.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/featured-website::featured-website-category.create'));

        return $formBuilder->create(FeaturedWebsiteCategoryForm::class)->renderForm();
    }

    public function store(FeaturedWebsiteCategoryRequest $request, BaseHttpResponse $response)
    {
        $featuredWebsiteCategory = $this->featuredWebsiteCategoryRepository->createOrUpdate($request->input());

        event(new CreatedContentEvent(FEATURED_WEBSITE_CATEGORY_MODULE_SCREEN_NAME, $request, $featuredWebsiteCategory));

        return $response
            ->setPreviousUrl(route('featured-website-category.index'))
            ->setNextUrl(route('featured-website-category.edit', $featuredWebsiteCategory->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit($id, FormBuilder $formBuilder, Request $request)
    {
        $featuredWebsiteCategory = $this->featuredWebsiteCategoryRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $featuredWebsiteCategory));

        PageTitle::setTitle(trans('plugins/featured-website::featured-website-category.edit') . ' "' . $featuredWebsiteCategory->name . '"');

        return $formBuilder->create(FeaturedWebsiteCategoryForm::class, ['model' => $featuredWebsiteCategory])->renderForm();
    }

    public function update($id, FeaturedWebsiteCategoryRequest $request, BaseHttpResponse $response)
    {
        $featuredWebsiteCategory = $this->featuredWebsiteCategoryRepository->findOrFail($id);

        $featuredWebsiteCategory->fill($request->input());

        $featuredWebsiteCategory = $this->featuredWebsiteCategoryRepository->createOrUpdate($featuredWebsiteCategory);

        event(new UpdatedContentEvent(FEATURED_WEBSITE_CATEGORY_MODULE_SCREEN_NAME, $request, $featuredWebsiteCategory));

        return $response
            ->setPreviousUrl(route('featured-website-category.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(Request $request, $id, BaseHttpResponse $response)
    {
        try {
            $featuredWebsiteCategory = $this->featuredWebsiteCategoryRepository->findOrFail($id);

            $this->featuredWebsiteCategoryRepository->delete($featuredWebsiteCategory);

            event(new DeletedContentEvent(FEATURED_WEBSITE_CATEGORY_MODULE_SCREEN_NAME, $request, $featuredWebsiteCategory));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function deletes(Request $request, BaseHttpResponse $response)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $response
                ->setError()
                ->setMessage(trans('core/base::notices.no_select'));
        }

        foreach ($ids as $id) {
            $featuredWebsiteCategory = $this->featuredWebsiteCategoryRepository->findOrFail($id);
            $this->featuredWebsiteCategoryRepository->delete($featuredWebsiteCategory);
            event(new DeletedContentEvent(FEATURED_WEBSITE_CATEGORY_MODULE_SCREEN_NAME, $request, $featuredWebsiteCategory));
        }

        return $response->setMessage(trans('core/base::notices.delete_success_message'));
    }
}
