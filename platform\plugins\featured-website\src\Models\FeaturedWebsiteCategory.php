<?php

namespace Shaqi\FeaturedWebsite\Models;

use <PERSON>haqi\Base\Traits\EnumCastable;
use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class FeaturedWebsiteCategory extends BaseModel
{
    use EnumCastable;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'featured_website_categories';

    /**
     * @var array
     */
    protected $fillable = [
        'name',
        'status',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'status' => BaseStatusEnum::class,
    ];

    /**
     * Get the featured websites that belong to this category.
     */
    public function featuredWebsites(): BelongsToMany
    {
        return $this->belongsToMany(FeaturedWebsite::class, 'featured_website_category_featured_website', 'category_id', 'featured_website_id');
    }
}
