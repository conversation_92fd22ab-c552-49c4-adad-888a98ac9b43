<?php

namespace Shaqi\FeaturedWebsite\Models;

use <PERSON>haqi\Base\Traits\EnumCastable;
use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use Shaqi\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class FeaturedWebsite extends BaseModel
{
    use EnumCastable;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'featured_websites';

    /**
     * @var array
     */
    protected $fillable = [
        'name',
        'content',
        'image',
        'order',
        'status',
    ];

    /**
     * @var array
     */
    protected $casts = [
        'status' => BaseStatusEnum::class,
    ];

    /**
     * Get the categories that belong to this featured website.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(FeaturedWebsiteCategory::class, 'featured_website_category_featured_website', 'featured_website_id', 'category_id');
    }
}
