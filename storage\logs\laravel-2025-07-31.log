[2025-07-31 12:05:48] local.ERROR: cURL error 6: Could not resolve host: api (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for api/check_connection_ext {"userId":2,"exception":"[object] (Illuminate\\Http\\Client\\ConnectionException(code: 0): cURL error 6: Could not resolve host: api (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for api/check_connection_ext at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:855)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(247): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(863): retry(0, Object(Closure), 100, Object(Closure))
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(728): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'api/check_conne...', Array)
#3 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(563): Illuminate\\Http\\Client\\PendingRequest->post('/api/check_conn...', Array)
#4 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(101): Shaqi\\Base\\Supports\\Core->createRequest('/api/check_conn...')
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(756): Shaqi\\Base\\Supports\\Core->Shaqi\\Base\\Supports\\{closure}()
#6 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(101): rescue(Object(Closure))
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): Shaqi\\Base\\Supports\\Core->Shaqi\\Base\\Supports\\{closure}()
#8 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(102): Illuminate\\Cache\\Repository->remember('license:45d0da5...', Object(Carbon\\Carbon), Object(Closure))
#9 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\setting\\src\\Http\\Controllers\\SettingController.php(265): Shaqi\\Base\\Supports\\Core->checkConnection()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\Setting\\Http\\Controllers\\SettingController->getVerifyLicense(Object(Illuminate\\Http\\Request), Object(Shaqi\\Base\\Supports\\Core), Object(Shaqi\\Base\\Http\\Responses\\BaseHttpResponse))
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getVerifyLicens...', Array)
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\Setting\\Http\\Controllers\\SettingController), 'getVerifyLicens...')
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#15 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 {main}

[previous exception] [object] (GuzzleHttp\\Exception\\ConnectException(code: 0): cURL error 6: Could not resolve host: api (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for api/check_connection_ext at D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:210)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(158): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(110): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1150): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1116): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1102): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(38): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(63): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#13 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#14 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#15 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(962): GuzzleHttp\\Client->request('POST', 'api/check_conne...', Array)
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(823): Illuminate\\Http\\Client\\PendingRequest->sendRequest('POST', 'api/check_conne...', Array)
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(247): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(863): retry(0, Object(Closure), 100, Object(Closure))
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(728): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'api/check_conne...', Array)
#21 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(563): Illuminate\\Http\\Client\\PendingRequest->post('/api/check_conn...', Array)
#22 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(101): Shaqi\\Base\\Supports\\Core->createRequest('/api/check_conn...')
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(756): Shaqi\\Base\\Supports\\Core->Shaqi\\Base\\Supports\\{closure}()
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(101): rescue(Object(Closure))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(397): Shaqi\\Base\\Supports\\Core->Shaqi\\Base\\Supports\\{closure}()
#26 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(102): Illuminate\\Cache\\Repository->remember('license:45d0da5...', Object(Carbon\\Carbon), Object(Closure))
#27 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\setting\\src\\Http\\Controllers\\SettingController.php(265): Shaqi\\Base\\Supports\\Core->checkConnection()
#28 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\Setting\\Http\\Controllers\\SettingController->getVerifyLicense(Object(Illuminate\\Http\\Request), Object(Shaqi\\Base\\Supports\\Core), Object(Shaqi\\Base\\Http\\Responses\\BaseHttpResponse))
#29 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getVerifyLicens...', Array)
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\Setting\\Http\\Controllers\\SettingController), 'getVerifyLicens...')
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#86 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#88 {main}
"} 
[2025-07-31 12:05:48] local.ERROR: cURL error 6: Could not resolve host: api (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for api/check_update {"userId":2,"exception":"[object] (Illuminate\\Http\\Client\\ConnectionException(code: 0): cURL error 6: Could not resolve host: api (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for api/check_update at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:855)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(247): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(863): retry(0, Object(Closure), 100, Object(Closure))
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(728): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'api/check_updat...', Array)
#3 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(563): Illuminate\\Http\\Client\\PendingRequest->post('/api/check_upda...', Array)
#4 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(227): Shaqi\\Base\\Supports\\Core->createRequest('/api/check_upda...', Array)
#5 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Controllers\\SystemController.php(162): Shaqi\\Base\\Supports\\Core->checkUpdate()
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\Base\\Http\\Controllers\\SystemController->getCheckUpdate(Object(Shaqi\\Base\\Http\\Responses\\BaseHttpResponse), Object(Shaqi\\Base\\Supports\\Core))
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getCheckUpdate', Array)
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\Base\\Http\\Controllers\\SystemController), 'getCheckUpdate')
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}

[previous exception] [object] (GuzzleHttp\\Exception\\ConnectException(code: 0): cURL error 6: Could not resolve host: api (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for api/check_update at D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:210)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(158): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(110): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1150): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1116): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1102): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(38): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(63): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#13 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#14 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#15 D:\\laragon\\www\\focusedcre\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(962): GuzzleHttp\\Client->request('POST', 'api/check_updat...', Array)
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(823): Illuminate\\Http\\Client\\PendingRequest->sendRequest('POST', 'api/check_updat...', Array)
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(247): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(863): retry(0, Object(Closure), 100, Object(Closure))
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(728): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'api/check_updat...', Array)
#21 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(563): Illuminate\\Http\\Client\\PendingRequest->post('/api/check_upda...', Array)
#22 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Supports\\Core.php(227): Shaqi\\Base\\Supports\\Core->createRequest('/api/check_upda...', Array)
#23 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Controllers\\SystemController.php(162): Shaqi\\Base\\Supports\\Core->checkUpdate()
#24 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\Base\\Http\\Controllers\\SystemController->getCheckUpdate(Object(Shaqi\\Base\\Http\\Responses\\BaseHttpResponse), Object(Shaqi\\Base\\Supports\\Core))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('getCheckUpdate', Array)
#26 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\Base\\Http\\Controllers\\SystemController), 'getCheckUpdate')
#27 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#29 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 {main}
"} 
[2025-07-31 12:06:16] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'focusedcre.featured_website_categories' doesn't exist (SQL: select `featured_website_categories`.`id` from `featured_website_categories` inner join `featured_website_category_featured_website` on `featured_website_categories`.`id` = `featured_website_category_featured_website`.`category_id` where `featured_website_category_featured_website`.`featured_website_id` = 103) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'focusedcre.featured_website_categories' doesn't exist (SQL: select `featured_website_categories`.`id` from `featured_website_categories` inner join `featured_website_category_featured_website` on `featured_website_categories`.`id` = `featured_website_category_featured_website`.`category_id` where `featured_website_category_featured_website`.`featured_website_id` = 103) at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select `feature...', Array, Object(Closure))
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select `feature...', Array, Object(Closure))
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select `feature...', Array, true)
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2920): Illuminate\\Database\\Query\\Builder->runSelect()
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2922): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(860): Illuminate\\Database\\Query\\Builder->pluck('featured_websit...', NULL)
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('featured_websit...')
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Shaqi\\Base\\Models\\BaseQueryBuilder), 'pluck', Array)
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(491): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Shaqi\\Base\\Models\\BaseQueryBuilder), 'pluck', Array)
#10 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Forms\\FeaturedWebsiteForm.php(22): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#11 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\FormBuilder.php(76): Shaqi\\FeaturedWebsite\\Forms\\FeaturedWebsiteForm->buildForm()
#12 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormBuilder.php(11): Kris\\LaravelFormBuilder\\FormBuilder->create('Shaqi\\\\FeaturedW...', Array, Array)
#13 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php(97): Shaqi\\Base\\Forms\\FormBuilder->create('Shaqi\\\\FeaturedW...', Array)
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController->edit('103', Object(Shaqi\\Base\\Forms\\FormBuilder), Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('edit', Array)
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController), 'edit')
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'focusedcre.featured_website_categories' doesn't exist at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:414)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): PDO->prepare('select `feature...')
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select `feature...', Array)
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('select `feature...', Array, Object(Closure))
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(422): Illuminate\\Database\\Connection->run('select `feature...', Array, Object(Closure))
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2706): Illuminate\\Database\\Connection->select('select `feature...', Array, true)
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2920): Illuminate\\Database\\Query\\Builder->runSelect()
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2922): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(860): Illuminate\\Database\\Query\\Builder->pluck('featured_websit...', NULL)
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->pluck('featured_websit...')
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Shaqi\\Base\\Models\\BaseQueryBuilder), 'pluck', Array)
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(491): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Shaqi\\Base\\Models\\BaseQueryBuilder), 'pluck', Array)
#12 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Forms\\FeaturedWebsiteForm.php(22): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('pluck', Array)
#13 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\FormBuilder.php(76): Shaqi\\FeaturedWebsite\\Forms\\FeaturedWebsiteForm->buildForm()
#14 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormBuilder.php(11): Kris\\LaravelFormBuilder\\FormBuilder->create('Shaqi\\\\FeaturedW...', Array, Array)
#15 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php(97): Shaqi\\Base\\Forms\\FormBuilder->create('Shaqi\\\\FeaturedW...', Array)
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController->edit('103', Object(Shaqi\\Base\\Forms\\FormBuilder), Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('edit', Array)
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController), 'edit')
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 {main}
"} 
[2025-07-31 12:07:33] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1059 Identifier name 'featured_website_category_featured_website_featured_website_id_index' is too long (SQL: alter table `featured_website_category_featured_website` add index `featured_website_category_featured_website_featured_website_id_index`(`featured_website_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1059 Identifier name 'featured_website_category_featured_website_featured_website_id_index' is too long (SQL: alter table `featured_website_category_featured_website` add index `featured_website_category_featured_website_featured_website_id_index`(`featured_website_id`)) at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('alter table `fe...', Array, Object(Closure))
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('alter table `fe...', Array, Object(Closure))
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement('alter table `fe...')
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build(Object(Shaqi\\Base\\Supports\\Database\\Blueprint))
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create('featured_websit...', Object(Closure))
#6 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\database\\migrations\\2024_01_15_100001_featured_website_create_category_pivot_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(488): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(406): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(415): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(751): Illuminate\\Console\\View\\Components\\Task->render('2024_01_15_1000...', Object(Closure))
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_15_1000...', Object(Closure))
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 36, false)
#15 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(628): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#25 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(1063): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(174): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\focusedcre\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1059 Identifier name 'featured_website_category_featured_website_featured_website_id_index' is too long at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `fe...', Array)
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('alter table `fe...', Array, Object(Closure))
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('alter table `fe...', Array, Object(Closure))
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement('alter table `fe...')
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build(Object(Shaqi\\Base\\Supports\\Database\\Blueprint))
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create('featured_websit...', Object(Closure))
#8 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\database\\migrations\\2024_01_15_100001_featured_website_create_category_pivot_table.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(488): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(406): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(415): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(751): Illuminate\\Console\\View\\Components\\Task->render('2024_01_15_1000...', Object(Closure))
#15 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_15_1000...', Object(Closure))
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 36, false)
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(628): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#27 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(1063): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(174): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\focusedcre\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-07-31 12:10:50] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'featured_website_category_featured_website' already exists (SQL: create table `featured_website_category_featured_website` (`category_id` bigint unsigned not null, `featured_website_id` bigint unsigned not null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'featured_website_category_featured_website' already exists (SQL: create table `featured_website_category_featured_website` (`category_id` bigint unsigned not null, `featured_website_id` bigint unsigned not null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:760)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('create table `f...', Array, Object(Closure))
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('create table `f...', Array, Object(Closure))
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement('create table `f...')
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build(Object(Shaqi\\Base\\Supports\\Database\\Blueprint))
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create('featured_websit...', Object(Closure))
#6 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\database\\migrations\\2024_01_15_100001_featured_website_create_category_pivot_table.php(19): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(488): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(406): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(415): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(751): Illuminate\\Console\\View\\Components\\Task->render('2024_01_15_1000...', Object(Closure))
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_15_1000...', Object(Closure))
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 36, false)
#15 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(628): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#25 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(1063): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(174): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\focusedcre\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'featured_website_category_featured_website' already exists at D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:545)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): PDOStatement->execute()
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `f...', Array)
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback('create table `f...', Array, Object(Closure))
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(546): Illuminate\\Database\\Connection->run('create table `f...', Array, Object(Closure))
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(109): Illuminate\\Database\\Connection->statement('create table `f...')
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(439): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(285): Illuminate\\Database\\Schema\\Builder->build(Object(Shaqi\\Base\\Supports\\Database\\Blueprint))
#7 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(338): Illuminate\\Database\\Schema\\Builder->create('featured_websit...', Object(Closure))
#8 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\database\\migrations\\2024_01_15_100001_featured_website_create_category_pivot_table.php(19): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(488): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(406): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(415): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(751): Illuminate\\Console\\View\\Components\\Task->render('2024_01_15_1000...', Object(Closure))
#15 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_15_1000...', Object(Closure))
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\laragon\\\\www\\\\...', 36, false)
#17 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(90): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(628): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(102): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#27 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(1063): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(174): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\laragon\\www\\focusedcre\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-07-31 12:16:26] local.ERROR: The "--database" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--database\" option does not exist. at D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Input\\ArgvInput.php(150): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('database', 'mysql')
#1 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--database=mysq...')
#2 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--database=mysq...', true)
#3 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Input\\Input.php(55): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(1063): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\DbCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(174): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\focusedcre\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 {main}
"} 
[2025-07-31 12:16:38] local.ERROR: TTY mode is not supported on Windows platform. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\RuntimeException(code: 0): TTY mode is not supported on Windows platform. at D:\\laragon\\www\\focusedcre\\vendor\\symfony\\process\\Process.php:1019)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\DbCommand.php(49): Symfony\\Component\\Process\\Process->setTty(true)
#1 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\DbCommand->handle()
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(183): Illuminate\\Container\\Container->call(Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(153): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(1063): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\DbCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\focusedcre\\vendor\\symfony\\console\\Application.php(174): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\laragon\\www\\focusedcre\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}
"} 
[2025-07-31 12:27:37] local.ERROR: Declaration of Shaqi\FeaturedWebsite\Tables\FeaturedWebsiteCategoryTable::ajax() must be compatible with Yajra\DataTables\Services\DataTable::ajax(): Illuminate\Http\JsonResponse {"userId":2,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Declaration of Shaqi\\FeaturedWebsite\\Tables\\FeaturedWebsiteCategoryTable::ajax() must be compatible with Yajra\\DataTables\\Services\\DataTable::ajax(): Illuminate\\Http\\JsonResponse at D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Tables\\FeaturedWebsiteCategoryTable.php:33)
[stacktrace]
#0 {main}
"} 
[2025-07-31 12:33:24] local.ERROR: Attempt to read property "id" on string {"view":{"view":"D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-checkbox-option-line.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1574905452 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3129</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1574905452\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","name":"<pre class=sf-dump id=sf-dump-764189731 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"12 characters\">categories[]</span>\"
</pre><script>Sfdump(\"sf-dump-764189731\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","nameKey":"<pre class=sf-dump id=sf-dump-1675613142 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
</pre><script>Sfdump(\"sf-dump-1675613142\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","type":"<pre class=sf-dump id=sf-dump-1229866259 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"13 characters\">categoryMulti</span>\"
</pre><script>Sfdump(\"sf-dump-1229866259\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","options":"<pre class=sf-dump id=sf-dump-1176289770 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>wrapper</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">form-group mb-3</span>\"
  </samp>]
  \"<span class=sf-dump-key>attr</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"12 characters\">form-control</span>\"
    <span class=sf-dump-key>0</span> => \"<span class=sf-dump-str title=\"5 characters\">v-pre</span>\"
  </samp>]
  \"<span class=sf-dump-key>help_block</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>text</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str>p</span>\"
    \"<span class=sf-dump-key>attr</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"10 characters\">help-block</span>\"
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>value</span>\" => []
  \"<span class=sf-dump-key>default_value</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"
  \"<span class=sf-dump-key>label_show</span>\" => <span class=sf-dump-const>true</span>
  \"<span class=sf-dump-key>is_child</span>\" => <span class=sf-dump-const>false</span>
  \"<span class=sf-dump-key>label_attr</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"13 characters\">control-label</span>\"
  </samp>]
  \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"11 characters\">text-danger</span>\"
  </samp>]
  \"<span class=sf-dump-key>rules</span>\" => []
  \"<span class=sf-dump-key>error_messages</span>\" => []
  \"<span class=sf-dump-key>choices</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"12 characters\">Landing Page</span>\"
    <span class=sf-dump-key>2</span> => \"<span class=sf-dump-str title=\"10 characters\">The Vitals</span>\"
    <span class=sf-dump-key>3</span> => \"<span class=sf-dump-str title=\"7 characters\">Optimal</span>\"
    <span class=sf-dump-key>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Property Website</span>\"
  </samp>]
  \"<span class=sf-dump-key>real_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">categories[]</span>\"
  \"<span class=sf-dump-key>wrapperAttrs</span>\" => \"<span class=sf-dump-str title=\"24 characters\">class=&quot;form-group mb-3&quot; </span>\"
  \"<span class=sf-dump-key>errorAttrs</span>\" => \"<span class=sf-dump-str title=\"20 characters\">class=&quot;text-danger&quot; </span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1176289770\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","showLabel":"<pre class=sf-dump id=sf-dump-541948515 data-indent-pad=\"  \"><span class=sf-dump-const>false</span>
</pre><script>Sfdump(\"sf-dump-541948515\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","showField":"<pre class=sf-dump id=sf-dump-1554102724 data-indent-pad=\"  \"><span class=sf-dump-const>true</span>
</pre><script>Sfdump(\"sf-dump-1554102724\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","showError":"<pre class=sf-dump id=sf-dump-302092189 data-indent-pad=\"  \"><span class=sf-dump-const>true</span>
</pre><script>Sfdump(\"sf-dump-302092189\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errorBag":"<pre class=sf-dump id=sf-dump-359450789 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"7 characters\">default</span>\"
</pre><script>Sfdump(\"sf-dump-359450789\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","translationTemplate":"<pre class=sf-dump id=sf-dump-1833579373 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1833579373\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categories":"<pre class=sf-dump id=sf-dump-106233200 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"12 characters\">Landing Page</span>\"
  <span class=sf-dump-key>2</span> => \"<span class=sf-dump-str title=\"10 characters\">The Vitals</span>\"
  <span class=sf-dump-key>3</span> => \"<span class=sf-dump-str title=\"7 characters\">Optimal</span>\"
  <span class=sf-dump-key>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Property Website</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-106233200\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","value":"<pre class=sf-dump id=sf-dump-213754742 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-213754742\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentId":"<pre class=sf-dump id=sf-dump-1831022394 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1831022394\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Attempt to read property \"id\" on string at D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-checkbox-option-line.blade.php:4)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#1 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-checkbox-option-line.blade.php(4): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-multi.blade.php(4): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#17 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Fields\\FormField.php(219): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\focusedcre\\platform\\core\\base\\resources\\views\\forms\\form.blade.php(56): Kris\\LaravelFormBuilder\\Fields\\FormField->render(Array, false)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#26 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(1013): Illuminate\\View\\View->render()
#27 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(422): Kris\\LaravelFormBuilder\\Form->render(Array, Array, true, true, true)
#28 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormAbstract.php(277): Kris\\LaravelFormBuilder\\Form->renderForm(Array, true, true, true)
#29 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php(97): Shaqi\\Base\\Forms\\FormAbstract->renderForm()
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController->edit('112', Object(Shaqi\\Base\\Forms\\FormBuilder), Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('edit', Array)
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController), 'edit')
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#34 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#88 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#90 {main}

[previous exception] [object] (ErrorException(code: 0): Attempt to read property \"id\" on string at D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\c0a7c52455cb1d30299c6a5b74f535e0d8100156.php:4)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#1 D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\c0a7c52455cb1d30299c6a5b74f535e0d8100156.php(4): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\9d51d2c3e45d1d7f7b31eb195dc6e74fa9fa603a.php(9): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#17 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Fields\\FormField.php(219): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\afb8128d33b079717d5ae3a156be79cfabcdac77.php(61): Kris\\LaravelFormBuilder\\Fields\\FormField->render(Array, false)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#26 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(1013): Illuminate\\View\\View->render()
#27 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(422): Kris\\LaravelFormBuilder\\Form->render(Array, Array, true, true, true)
#28 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormAbstract.php(277): Kris\\LaravelFormBuilder\\Form->renderForm(Array, true, true, true)
#29 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php(97): Shaqi\\Base\\Forms\\FormAbstract->renderForm()
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController->edit('112', Object(Shaqi\\Base\\Forms\\FormBuilder), Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('edit', Array)
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController), 'edit')
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#34 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#88 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#90 {main}
"} 
[2025-07-31 12:34:54] local.ERROR: Attempt to read property "id" on string {"view":{"view":"D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-checkbox-option-line.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-14246203 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3129</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-14246203\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","name":"<pre class=sf-dump id=sf-dump-1879718718 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"12 characters\">categories[]</span>\"
</pre><script>Sfdump(\"sf-dump-1879718718\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","nameKey":"<pre class=sf-dump id=sf-dump-457523528 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"10 characters\">categories</span>\"
</pre><script>Sfdump(\"sf-dump-457523528\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","type":"<pre class=sf-dump id=sf-dump-1748226973 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"13 characters\">categoryMulti</span>\"
</pre><script>Sfdump(\"sf-dump-1748226973\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","options":"<pre class=sf-dump id=sf-dump-1405582517 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>wrapper</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"15 characters\">form-group mb-3</span>\"
  </samp>]
  \"<span class=sf-dump-key>attr</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"12 characters\">form-control</span>\"
    <span class=sf-dump-key>0</span> => \"<span class=sf-dump-str title=\"5 characters\">v-pre</span>\"
  </samp>]
  \"<span class=sf-dump-key>help_block</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>text</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str>p</span>\"
    \"<span class=sf-dump-key>attr</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"10 characters\">help-block</span>\"
    </samp>]
  </samp>]
  \"<span class=sf-dump-key>value</span>\" => []
  \"<span class=sf-dump-key>default_value</span>\" => <span class=sf-dump-const>null</span>
  \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Categories</span>\"
  \"<span class=sf-dump-key>label_show</span>\" => <span class=sf-dump-const>true</span>
  \"<span class=sf-dump-key>is_child</span>\" => <span class=sf-dump-const>false</span>
  \"<span class=sf-dump-key>label_attr</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"13 characters\">control-label</span>\"
  </samp>]
  \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"11 characters\">text-danger</span>\"
  </samp>]
  \"<span class=sf-dump-key>rules</span>\" => []
  \"<span class=sf-dump-key>error_messages</span>\" => []
  \"<span class=sf-dump-key>choices</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"12 characters\">Landing Page</span>\"
    <span class=sf-dump-key>2</span> => \"<span class=sf-dump-str title=\"10 characters\">The Vitals</span>\"
    <span class=sf-dump-key>3</span> => \"<span class=sf-dump-str title=\"7 characters\">Optimal</span>\"
    <span class=sf-dump-key>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Property Website</span>\"
  </samp>]
  \"<span class=sf-dump-key>real_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">categories[]</span>\"
  \"<span class=sf-dump-key>wrapperAttrs</span>\" => \"<span class=sf-dump-str title=\"24 characters\">class=&quot;form-group mb-3&quot; </span>\"
  \"<span class=sf-dump-key>errorAttrs</span>\" => \"<span class=sf-dump-str title=\"20 characters\">class=&quot;text-danger&quot; </span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1405582517\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","showLabel":"<pre class=sf-dump id=sf-dump-1246332112 data-indent-pad=\"  \"><span class=sf-dump-const>false</span>
</pre><script>Sfdump(\"sf-dump-1246332112\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","showField":"<pre class=sf-dump id=sf-dump-1669628215 data-indent-pad=\"  \"><span class=sf-dump-const>true</span>
</pre><script>Sfdump(\"sf-dump-1669628215\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","showError":"<pre class=sf-dump id=sf-dump-74759623 data-indent-pad=\"  \"><span class=sf-dump-const>true</span>
</pre><script>Sfdump(\"sf-dump-74759623\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","errorBag":"<pre class=sf-dump id=sf-dump-1541003417 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"7 characters\">default</span>\"
</pre><script>Sfdump(\"sf-dump-1541003417\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","translationTemplate":"<pre class=sf-dump id=sf-dump-1069858286 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1069858286\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","categories":"<pre class=sf-dump id=sf-dump-503824219 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"12 characters\">Landing Page</span>\"
  <span class=sf-dump-key>2</span> => \"<span class=sf-dump-str title=\"10 characters\">The Vitals</span>\"
  <span class=sf-dump-key>3</span> => \"<span class=sf-dump-str title=\"7 characters\">Optimal</span>\"
  <span class=sf-dump-key>4</span> => \"<span class=sf-dump-str title=\"16 characters\">Property Website</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-503824219\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","value":"<pre class=sf-dump id=sf-dump-1325986867 data-indent-pad=\"  \">[]
</pre><script>Sfdump(\"sf-dump-1325986867\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","currentId":"<pre class=sf-dump id=sf-dump-198100358 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-198100358\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Attempt to read property \"id\" on string at D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-checkbox-option-line.blade.php:4)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#1 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-checkbox-option-line.blade.php(4): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-multi.blade.php(4): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#17 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Fields\\FormField.php(219): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\focusedcre\\platform\\core\\base\\resources\\views\\forms\\form.blade.php(56): Kris\\LaravelFormBuilder\\Fields\\FormField->render(Array, false)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#26 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(1013): Illuminate\\View\\View->render()
#27 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(422): Kris\\LaravelFormBuilder\\Form->render(Array, Array, true, true, true)
#28 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormAbstract.php(277): Kris\\LaravelFormBuilder\\Form->renderForm(Array, true, true, true)
#29 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php(97): Shaqi\\Base\\Forms\\FormAbstract->renderForm()
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController->edit('112', Object(Shaqi\\Base\\Forms\\FormBuilder), Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('edit', Array)
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController), 'edit')
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#34 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#88 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#90 {main}

[previous exception] [object] (ErrorException(code: 0): Attempt to read property \"id\" on string at D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\c0a7c52455cb1d30299c6a5b74f535e0d8100156.php:4)
[stacktrace]
#0 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#1 D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\c0a7c52455cb1d30299c6a5b74f535e0d8100156.php(4): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'D:\\\\laragon\\\\www\\\\...', 4)
#2 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#5 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#6 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#7 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#8 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#9 D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\9d51d2c3e45d1d7f7b31eb195dc6e74fa9fa603a.php(9): Illuminate\\View\\View->render()
#10 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#11 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#12 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#13 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#14 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#15 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#16 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#17 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Fields\\FormField.php(219): Illuminate\\View\\View->render()
#18 D:\\laragon\\www\\focusedcre\\storage\\framework\\views\\afb8128d33b079717d5ae3a156be79cfabcdac77.php(61): Kris\\LaravelFormBuilder\\Fields\\FormField->render(Array, false)
#19 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(109): require('D:\\\\laragon\\\\www\\\\...')
#20 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#23 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#24 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#25 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(147): Shaqi\\Shortcode\\View\\View->renderContents()
#26 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(1013): Illuminate\\View\\View->render()
#27 D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\Form.php(422): Kris\\LaravelFormBuilder\\Form->render(Array, Array, true, true, true)
#28 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormAbstract.php(277): Kris\\LaravelFormBuilder\\Form->renderForm(Array, true, true, true)
#29 D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php(97): Shaqi\\Base\\Forms\\FormAbstract->renderForm()
#30 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController->edit('112', Object(Shaqi\\Base\\Forms\\FormBuilder), Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('edit', Array)
#32 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(260): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController), 'edit')
#33 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#34 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Routing\\Route->run()
#35 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\focusedcre\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\acl\\src\\Http\\Middleware\\Authenticate.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\ACL\\Http\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#56 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(740): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#65 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(190): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Shaqi\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 D:\\laragon\\www\\focusedcre\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#85 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#88 D:\\laragon\\www\\focusedcre\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\focusedcre\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#90 {main}
"} 
