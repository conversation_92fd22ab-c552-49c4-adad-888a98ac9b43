{"__meta": {"id": "Xe6b325f8a7977b1cebee8458729b808d", "datetime": "2025-07-31 13:18:54", "utime": 1753967934.921521, "method": "GET", "uri": "/admin/featured-websites/edit/50", "ip": "127.0.0.1"}, "php": {"version": "8.1.4", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753967933.63452, "end": 1753967934.921546, "duration": 1.2870259284973145, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": 1753967933.63452, "relative_start": 0, "end": 1753967934.426925, "relative_end": 1753967934.426925, "duration": 0.7924048900604248, "duration_str": "792ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753967934.427414, "relative_start": 0.7928938865661621, "end": 1753967934.921549, "relative_end": 3.0994415283203125e-06, "duration": 0.49413514137268066, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 55451496, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 50, "templates": [{"name": "core/base::forms.form (\\platform\\core\\base\\resources\\views\\forms\\form.blade.php)", "param_count": 8, "params": ["showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/form.blade.php:0"}, {"name": "laravel-form-builder::text (\\vendor\\kris\\laravel-form-builder\\src\\views\\text.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "php", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder/../../views/text.php:0"}, {"name": "core/base::forms.fields.html (\\platform\\core\\base\\resources\\views\\forms\\fields\\html.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/fields/html.blade.php:0"}, {"name": "core/base::forms.fields.custom-select (\\platform\\core\\base\\resources\\views\\forms\\fields\\custom-select.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/fields/custom-select.blade.php:0"}, {"name": "core/base::forms.partials.custom-select (\\platform\\core\\base\\resources\\views\\forms\\partials\\custom-select.blade.php)", "param_count": 6, "params": ["name", "choices", "selected", "selectAttributes", "optionsAttributes", "optgroupsAttributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/custom-select.blade.php:0"}, {"name": "core/base::forms.partials.help-block (\\platform\\core\\base\\resources\\views\\forms\\partials\\help-block.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate", "emptyVal"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/help-block.blade.php:0"}, {"name": "core/base::forms.partials.errors (\\platform\\core\\base\\resources\\views\\forms\\partials\\errors.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate", "emptyVal"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/errors.blade.php:0"}, {"name": "laravel-form-builder::text (\\vendor\\kris\\laravel-form-builder\\src\\views\\text.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "php", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder/../../views/text.php:0"}, {"name": "core/base::forms.fields.html (\\platform\\core\\base\\resources\\views\\forms\\fields\\html.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/fields/html.blade.php:0"}, {"name": "core/base::forms.fields.editor (\\platform\\core\\base\\resources\\views\\forms\\fields\\editor.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/fields/editor.blade.php:0"}, {"name": "core/base::forms.partials.editor (\\platform\\core\\base\\resources\\views\\forms\\partials\\editor.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/editor.blade.php:0"}, {"name": "core/base::elements.loading (\\platform\\core\\base\\resources\\views\\elements\\loading.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "name", "value", "attributes", "result", "__currentLoopData", "item", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/elements/loading.blade.php:0"}, {"name": "core/base::forms.partials.tinymce (\\platform\\core\\base\\resources\\views\\forms\\partials\\tinymce.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/tinymce.blade.php:0"}, {"name": "core/base::forms.partials.help-block (\\platform\\core\\base\\resources\\views\\forms\\partials\\help-block.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/help-block.blade.php:0"}, {"name": "core/base::forms.partials.errors (\\platform\\core\\base\\resources\\views\\forms\\partials\\errors.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/errors.blade.php:0"}, {"name": "core/base::elements.meta-box (\\platform\\core\\base\\resources\\views\\elements\\meta-box.blade.php)", "param_count": 2, "params": ["data", "context"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/elements/meta-box.blade.php:0"}, {"name": "core/base::forms.partials.form-actions (\\platform\\core\\base\\resources\\views\\forms\\partials\\form-actions.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/form-actions.blade.php:0"}, {"name": "core/base::layouts.partials.breadcrumbs (\\platform\\core\\base\\resources\\views\\layouts\\partials\\breadcrumbs.blade.php)", "param_count": 1, "params": ["breadcrumbs"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.php:0"}, {"name": "core/base::elements.meta-box (\\platform\\core\\base\\resources\\views\\elements\\meta-box.blade.php)", "param_count": 2, "params": ["data", "context"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/elements/meta-box.blade.php:0"}, {"name": "core/base::forms.fields.custom-select (\\platform\\core\\base\\resources\\views\\forms\\fields\\custom-select.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/fields/custom-select.blade.php:0"}, {"name": "core/base::forms.partials.custom-select (\\platform\\core\\base\\resources\\views\\forms\\partials\\custom-select.blade.php)", "param_count": 6, "params": ["name", "choices", "selected", "selectAttributes", "optionsAttributes", "optgroupsAttributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/custom-select.blade.php:0"}, {"name": "core/base::forms.partials.help-block (\\platform\\core\\base\\resources\\views\\forms\\partials\\help-block.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate", "emptyVal"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/help-block.blade.php:0"}, {"name": "core/base::forms.partials.errors (\\platform\\core\\base\\resources\\views\\forms\\partials\\errors.blade.php)", "param_count": 13, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate", "emptyVal"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/errors.blade.php:0"}, {"name": "core/base::forms.fields.media-image (\\platform\\core\\base\\resources\\views\\forms\\fields\\media-image.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/fields/media-image.blade.php:0"}, {"name": "core/base::forms.partials.image (\\platform\\core\\base\\resources\\views\\forms\\partials\\image.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/image.blade.php:0"}, {"name": "core/base::forms.partials.help-block (\\platform\\core\\base\\resources\\views\\forms\\partials\\help-block.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/help-block.blade.php:0"}, {"name": "core/base::forms.partials.errors (\\platform\\core\\base\\resources\\views\\forms\\partials\\errors.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/errors.blade.php:0"}, {"name": "core/base::forms.fields.media-image (\\platform\\core\\base\\resources\\views\\forms\\fields\\media-image.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/fields/media-image.blade.php:0"}, {"name": "core/base::forms.partials.image (\\platform\\core\\base\\resources\\views\\forms\\partials\\image.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/image.blade.php:0"}, {"name": "core/base::forms.partials.help-block (\\platform\\core\\base\\resources\\views\\forms\\partials\\help-block.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/help-block.blade.php:0"}, {"name": "core/base::forms.partials.errors (\\platform\\core\\base\\resources\\views\\forms\\partials\\errors.blade.php)", "param_count": 12, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/errors.blade.php:0"}, {"name": "plugins/featured-website::categories.categories-multi (\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-multi.blade.php)", "param_count": 9, "params": ["name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/categories/categories-multi.blade.php:0"}, {"name": "plugins/featured-website::categories.categories-checkbox-option-line (\\platform\\plugins\\featured-website\\resources\\views\\categories\\categories-checkbox-option-line.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "name", "<PERSON><PERSON><PERSON>", "type", "options", "showLabel", "showField", "showError", "errorBag", "translationTemplate", "categories", "value", "currentId"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/categories/categories-checkbox-option-line.blade.php:0"}, {"name": "core/base::elements.meta-box (\\platform\\core\\base\\resources\\views\\elements\\meta-box.blade.php)", "param_count": 2, "params": ["data", "context"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/elements/meta-box.blade.php:0"}, {"name": "core/js-validation::bootstrap (\\platform\\core\\js-validation\\resources\\views\\bootstrap.blade.php)", "param_count": 1, "params": ["validator"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/js-validation/resources/views/bootstrap.blade.php:0"}, {"name": "core/base::layouts.master (\\platform\\core\\base\\resources\\views\\layouts\\master.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/master.blade.php:0"}, {"name": "core/base::layouts.partials.top-header (\\platform\\core\\base\\resources\\views\\layouts\\partials\\top-header.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop", "themes", "activeTheme"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/top-header.blade.php:0"}, {"name": "core/base::layouts.partials.top-menu (\\platform\\core\\base\\resources\\views\\layouts\\partials\\top-menu.blade.php)", "param_count": 17, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop", "themes", "activeTheme"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/top-menu.blade.php:0"}, {"name": "core/base::notification.notification (\\platform\\core\\base\\resources\\views\\notification\\notification.blade.php)", "param_count": 1, "params": ["countNotificationUnread"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/notification/notification.blade.php:0"}, {"name": "plugins/contact::partials.notification (\\platform\\plugins\\contact\\resources\\views\\partials\\notification.blade.php)", "param_count": 1, "params": ["contacts"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/contact/resources/views/partials/notification.blade.php:0"}, {"name": "core/base::layouts.partials.sidebar (\\platform\\core\\base\\resources\\views\\layouts\\partials\\sidebar.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/sidebar.blade.php:0"}, {"name": "core/base::layouts.partials.breadcrumbs (\\platform\\core\\base\\resources\\views\\layouts\\partials\\breadcrumbs.blade.php)", "param_count": 1, "params": ["breadcrumbs"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.php:0"}, {"name": "core/base::layouts.partials.footer (\\platform\\core\\base\\resources\\views\\layouts\\partials\\footer.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/footer.blade.php:0"}, {"name": "core/media::partials.media (\\platform\\core\\media\\resources\\views\\partials\\media.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/media/resources/views/partials/media.blade.php:0"}, {"name": "core/media::config (\\platform\\core\\media\\resources\\views\\config.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/media/resources/views/config.blade.php:0"}, {"name": "core/base::layouts.base (\\platform\\core\\base\\resources\\views\\layouts\\base.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/base.blade.php:0"}, {"name": "assets::header (\\vendor\\shaqi\\assets\\resources\\views\\header.blade.php)", "param_count": 2, "params": ["styles", "headScripts"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\assets\\src\\Providers/../../resources/views/header.blade.php:0"}, {"name": "core/base::elements.common (\\platform\\core\\base\\resources\\views\\elements\\common.blade.php)", "param_count": 15, "params": ["__env", "app", "errors", "showStart", "showFields", "showEnd", "formOptions", "fields", "model", "exclude", "form", "__currentLoopData", "field", "key", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/elements/common.blade.php:0"}, {"name": "assets::footer (\\vendor\\shaqi\\assets\\resources\\views\\footer.blade.php)", "param_count": 1, "params": ["bodyScripts"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\assets\\src\\Providers/../../resources/views/footer.blade.php:0"}, {"name": "core/base::notification.notification-content (\\platform\\core\\base\\resources\\views\\notification\\notification-content.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/notification/notification-content.blade.php:0"}]}, "route": {"uri": "GET admin/featured-websites/edit/{featured_website}", "middleware": "web, core, auth", "as": "featured-website.edit", "controller": "Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController@edit", "namespace": "Shaqi\\FeaturedWebsite\\Http\\Controllers", "prefix": "admin/featured-websites", "where": [], "file": "<a href=\"vscode://file/D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:88\">\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:88-98</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.00909, "accumulated_duration_str": "9.09ms", "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 12}], "duration": 0.0034500000000000004, "duration_str": "3.45ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "focusedcre", "start_percent": 0, "width_percent": 37.954}, {"sql": "select * from `featured_websites` where `id` = '50' limit 1", "type": "query", "params": [], "bindings": ["50"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 81}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 100}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 91}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.00062, "duration_str": "620μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:81", "connection": "focusedcre", "start_percent": 37.954, "width_percent": 6.821}, {"sql": "select `featured_website_categories`.`id` from `featured_website_categories` inner join `featured_website_category_featured_website` on `featured_website_categories`.`id` = `featured_website_category_featured_website`.`category_id` where `featured_website_category_featured_website`.`featured_website_id` = 50", "type": "query", "params": [], "bindings": ["50"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Forms\\FeaturedWebsiteForm.php", "line": 22}, {"index": 17, "namespace": null, "name": "\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\FormBuilder.php", "line": 76}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 97}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\platform\\plugins\\featured-website\\src\\Forms\\FeaturedWebsiteForm.php:22", "connection": "focusedcre", "start_percent": 44.774, "width_percent": 7.921}, {"sql": "select `name`, `id` from `featured_website_categories` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Forms\\FeaturedWebsiteForm.php", "line": 65}, {"index": 14, "namespace": null, "name": "\\vendor\\kris\\laravel-form-builder\\src\\Kris\\LaravelFormBuilder\\FormBuilder.php", "line": 76}, {"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Forms\\FormBuilder.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 97}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\platform\\plugins\\featured-website\\src\\Forms\\FeaturedWebsiteForm.php:65", "connection": "focusedcre", "start_percent": 52.695, "width_percent": 4.95}, {"sql": "select `meta_value` from `meta_boxes` where `meta_key` = 'website_logo' and `reference_id` = 50 and `reference_type` = 'Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite' limit 1", "type": "query", "params": [], "bindings": ["website_logo", "50", "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 187}, {"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 22, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\functions.php", "line": 425}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 57.646, "width_percent": 5.281}, {"sql": "select `meta_value` from `meta_boxes` where `meta_key` = 'website_tag' and `reference_id` = 50 and `reference_type` = 'Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite' limit 1", "type": "query", "params": [], "bindings": ["website_tag", "50", "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 187}, {"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 22, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\functions.php", "line": 426}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 62.926, "width_percent": 4.4}, {"sql": "select `meta_value` from `meta_boxes` where `meta_key` = 'website_button_link' and `reference_id` = 50 and `reference_type` = 'Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite' limit 1", "type": "query", "params": [], "bindings": ["website_button_link", "50", "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 187}, {"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 168}, {"index": 22, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\functions.php", "line": 427}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 67.327, "width_percent": 4.29}, {"sql": "select `id` from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 114}, {"index": 16, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 110}, {"index": 17, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Providers\\HookServiceProvider.php", "line": 49}, {"index": 21, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:114", "connection": "focusedcre", "start_percent": 71.617, "width_percent": 4.62}, {"sql": "select * from `field_groups` where (`status` = 'published') order by `order` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Repositories\\Eloquent\\FieldGroupRepository.php", "line": 48}, {"index": 17, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Repositories\\Caches\\FieldGroupCacheDecorator.php", "line": 15}, {"index": 18, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 202}, {"index": 20, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\helpers.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Providers\\HookServiceProvider.php", "line": 77}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\platform\\plugins\\custom-field\\src\\Repositories\\Eloquent\\FieldGroupRepository.php:48", "connection": "focusedcre", "start_percent": 76.238, "width_percent": 5.061}, {"sql": "select count(*) as aggregate from `admin_notifications` where `read_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Models\\AdminNotification.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Providers\\BaseServiceProvider.php", "line": 199}, {"index": 20, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\vendor\\shaqi\\platform\\base\\src\\Models\\AdminNotification.php:52", "connection": "focusedcre", "start_percent": 81.298, "width_percent": 3.85}, {"sql": "select count(*) as aggregate from `contacts` where `status` = 'unread'", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\contact\\src\\Providers\\HookServiceProvider.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 85.149, "width_percent": 6.271}, {"sql": "select `id`, `name`, `email`, `phone`, `created_at` from `contacts` where `status` = 'unread' order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\contact\\src\\Providers\\HookServiceProvider.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 91.419, "width_percent": 4.18}, {"sql": "select count(*) as aggregate from `ec_orders` where `status` = 'pending' and `is_finished` = 1", "type": "query", "params": [], "bindings": ["pending", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 767}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 95.6, "width_percent": 4.4}]}, "models": {"data": {"Shaqi\\Contact\\Models\\Contact": 10, "Shaqi\\CustomField\\Models\\FieldGroup": 5, "Shaqi\\ACL\\Models\\Role": 2, "Shaqi\\Base\\Models\\MetaBox": 3, "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite": 1, "Shaqi\\ACL\\Models\\User": 1}, "count": 22}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"https://focusedcre.local/admin/featured-websites/edit/50\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "admin-theme": "default", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/featured-websites/edit/50", "status_code": "<pre class=sf-dump id=sf-dump-1673927041 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1673927041\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-932125969 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-932125969\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-732673498 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-732673498\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1031143859 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">https://focusedcre.local/admin/featured-websites/edit/50</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2668 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0; XSRF-TOKEN=eyJpdiI6Ii9ZYmEzVkw2N0kya0JBR3h0QVJiQXc9PSIsInZhbHVlIjoiV1dMMkVJUEJjUVdhRFA4MUJhd2YzeVFsRW5qV2dFZWdodUl1WTRuKytxOUEwMzBuZXF4dGlWNmU4UytKMUwzdnMrN2x0a0hZS2lDdERwOW5hZUdrTnVBN3diZ2JrL0MvN3BGVjZXcEgwOERwSlU3UGJncGI2djZ4TWlPMnU2WlYiLCJtYWMiOiIwZmU2NWE2NzY4NzA5MGFmZjY3ZjU4ZWMxN2UzMjI3NTIzOTNmMjUxMDU3YTQyNDNlMDA2MmQ4YTg3NWVmNDg1IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IjdFeVpkMjZraGw1OWFMUHlOWjFaWXc9PSIsInZhbHVlIjoiZ21MdDRmRzBobHRnNlpydXQ5MnhEOXR6eEwxV0FCSTk0SDd1am93NmFOV1NHYmMrUGF4VkVqUlRRZk9idnNNQ2lWSmV0U1lWbS9VUkc3a1Zvd05vKzBvUG1HLzljekFtcVN3MXU5bkdqZUoyOGNKcm5xenBqK0YzdWpwOFlKTzQiLCJtYWMiOiI5NjFhZTMxNmNjM2Q0NDM0NGJmY2Y2MWMyNTE1MjhiMDI2OTgzMjQ1MDQwYzZjYjllMzRlZjA0NTdkMWUwZDIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031143859\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1871778809 data-indent-pad=\"  \"><span class=sf-dump-note>array:50</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://focusedcre.local/admin/featured-websites/edit/50</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2668 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0; XSRF-TOKEN=eyJpdiI6Ii9ZYmEzVkw2N0kya0JBR3h0QVJiQXc9PSIsInZhbHVlIjoiV1dMMkVJUEJjUVdhRFA4MUJhd2YzeVFsRW5qV2dFZWdodUl1WTRuKytxOUEwMzBuZXF4dGlWNmU4UytKMUwzdnMrN2x0a0hZS2lDdERwOW5hZUdrTnVBN3diZ2JrL0MvN3BGVjZXcEgwOERwSlU3UGJncGI2djZ4TWlPMnU2WlYiLCJtYWMiOiIwZmU2NWE2NzY4NzA5MGFmZjY3ZjU4ZWMxN2UzMjI3NTIzOTNmMjUxMDU3YTQyNDNlMDA2MmQ4YTg3NWVmNDg1IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IjdFeVpkMjZraGw1OWFMUHlOWjFaWXc9PSIsInZhbHVlIjoiZ21MdDRmRzBobHRnNlpydXQ5MnhEOXR6eEwxV0FCSTk0SDd1am93NmFOV1NHYmMrUGF4VkVqUlRRZk9idnNNQ2lWSmV0U1lWbS9VUkc3a1Zvd05vKzBvUG1HLzljekFtcVN3MXU5bkdqZUoyOGNKcm5xenBqK0YzdWpwOFlKTzQiLCJtYWMiOiI5NjFhZTMxNmNjM2Q0NDM0NGJmY2Y2MWMyNTE1MjhiMDI2OTgzMjQ1MDQwYzZjYjllMzRlZjA0NTdkMWUwZDIxIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1315 characters\">C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\ProgramData\\ComposerSetup\\bin;C:\\Program Files\\Java\\jdk-15.0.2;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Amazon\\AWSCLIV2\\;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\laragon\\bin\\composer;D:\\laragon\\bin\\git\\bin;D:\\laragon\\bin\\git\\cmd;D:\\laragon\\bin\\git\\mingw64\\bin;D:\\laragon\\bin\\git\\usr\\bin;D:\\laragon\\bin\\mongodb\\mongodb-4.0.3\\;D:\\laragon\\bin\\mysql\\mariadb-10.6.7-winx64\\bin;D:\\laragon\\bin\\ngrok;D:\\laragon\\bin\\nodejs\\node-v16.16.0;D:\\laragon\\bin\\php\\php-8.1.4-Win32-vs16-x64;D:\\laragon\\bin\\postgresql\\postgresql\\bin;D:\\laragon\\bin\\python\\python-3.13;D:\\laragon\\bin\\python\\python-3.13\\Scripts;D:\\laragon\\bin\\yarn\\bin;D:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.47 (Win64) OpenSSL/1.1.1m PHP/8.1.4</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">D:/laragon/www/focusedcre/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">26393</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/admin/featured-websites/edit/50</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/admin/featured-websites/edit/50</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753967933.6345</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753967933</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1871778809\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2054185200 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">276c8eb96ecbc99bdaa7c37095beb04735960841</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;footprint&quot;:&quot;276c8eb96ecbc99bdaa7c37095beb04735960841&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;focusedcre.local&quot;,&quot;landing_page&quot;:&quot;new-website-request&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|6LfPDjUyZLXD0DhNLxRR7WMt2NSfbdUnceBmo2YcfbIcDul5oc1icuy6Y0d2|$2y$10$v3mwe1ya6fDmDwB5vikHTuie26Jjsc.ZMD/k9JtQCyVybjxTgiKCu</span>\"\n  \"<span class=sf-dump-key>_gid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_94P3KEDHFG</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">801BcPCBI6AAQOWuMaIoY4d3XHvjwOixQ6zrnhTO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054185200\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1799654837 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 13:18:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImJTY0ZlRHlMZFBGTk1OWkVvOFhETnc9PSIsInZhbHVlIjoidmgzTmlmL3NTQ0R3UFh2ODR2SzdZaFFiK1g5TWdrbitENDFVK2IwdzhBSW5EM2JsSHRlRVd4YVlsSklkdVFhWnd3YzJCR2NUZXRWanp0UEtUSGdxSjB2RVpMNzhCcUlJTVRmc0NvaElzTVAvTU43TlpnN0w2OTVJdzNjN0NsQ0giLCJtYWMiOiIyNWJjMjNlY2Y4NTMyZWY0OGZlZWY5NDI2M2Q1MjI5YTA0ZmU1YWUzOGY5OGMwZmIzMWRkMDZmNzBiYzgwZDc5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 15:18:54 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shaqi_session=eyJpdiI6Ikw5QitMNjREbk9ZaDdkazBPYXQxZHc9PSIsInZhbHVlIjoibDJ0UFA0cExrWHZjWlNQZi9MYmx4Z1ZvQWY2TFRvdG81SmFNbWpFTlR1VjkwWloxVGFCMDhMWDMzY1NiaFpQL3dEdzhqU2p6VUFHK0dnK2IzNXhEa0RiTGU4SGY1RzNONWs2Y1R3aHB2eldRcFNyVENMYmYvZWdFR29KM3FNTDQiLCJtYWMiOiJmZmUzY2YwODY3MmU0N2MxMzU2Y2M3YWJmYzdjMDYyZGU2MDlkYzdhMTVhOGYxMzFmMmRlZDFlNWViMWEyN2U4IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 15:18:54 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImJTY0ZlRHlMZFBGTk1OWkVvOFhETnc9PSIsInZhbHVlIjoidmgzTmlmL3NTQ0R3UFh2ODR2SzdZaFFiK1g5TWdrbitENDFVK2IwdzhBSW5EM2JsSHRlRVd4YVlsSklkdVFhWnd3YzJCR2NUZXRWanp0UEtUSGdxSjB2RVpMNzhCcUlJTVRmc0NvaElzTVAvTU43TlpnN0w2OTVJdzNjN0NsQ0giLCJtYWMiOiIyNWJjMjNlY2Y4NTMyZWY0OGZlZWY5NDI2M2Q1MjI5YTA0ZmU1YWUzOGY5OGMwZmIzMWRkMDZmNzBiYzgwZDc5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 15:18:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">shaqi_session=eyJpdiI6Ikw5QitMNjREbk9ZaDdkazBPYXQxZHc9PSIsInZhbHVlIjoibDJ0UFA0cExrWHZjWlNQZi9MYmx4Z1ZvQWY2TFRvdG81SmFNbWpFTlR1VjkwWloxVGFCMDhMWDMzY1NiaFpQL3dEdzhqU2p6VUFHK0dnK2IzNXhEa0RiTGU4SGY1RzNONWs2Y1R3aHB2eldRcFNyVENMYmYvZWdFR29KM3FNTDQiLCJtYWMiOiJmZmUzY2YwODY3MmU0N2MxMzU2Y2M3YWJmYzdjMDYyZGU2MDlkYzdhMTVhOGYxMzFmMmRlZDFlNWViMWEyN2U4IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 15:18:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799654837\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-573905996 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://focusedcre.local/admin/featured-websites/edit/50</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin-theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-573905996\", {\"maxDepth\":0})</script>\n"}}