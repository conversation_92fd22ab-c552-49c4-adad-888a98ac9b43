<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First, drop the existing table if it exists
        Schema::dropIfExists('featured_website_category_featured_website');
        
        // Then create it properly with correct index names
        Schema::create('featured_website_category_featured_website', function (Blueprint $table) {
            $table->foreignId('category_id')->index('fw_cat_pivot_cat_idx');
            $table->foreignId('featured_website_id')->index('fw_cat_pivot_fw_idx');
            $table->primary(['category_id', 'featured_website_id'], 'fw_cat_pivot_pk');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('featured_website_category_featured_website');
    }
};
