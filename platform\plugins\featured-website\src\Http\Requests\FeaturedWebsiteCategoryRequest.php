<?php

namespace Shaqi\FeaturedWebsite\Http\Requests;

use Shaqi\Base\Enums\BaseStatusEnum;
use Shaqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class FeaturedWebsiteCategoryRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name'   => 'required|string|max:255',
            'status' => Rule::in(BaseStatusEnum::values()),
        ];
    }
}
