<?php
use <PERSON><PERSON>qi\FeaturedWebsite\Models\FeaturedWebsite;
Route::group(['namespace' => 'Shaqi\FeaturedWebsite\Http\Controllers', 'middleware' => ['web', 'core']], function () {

    Route::group(['prefix' => BaseHelper::getAdminPrefix(), 'middleware' => 'auth'], function () {

        Route::group(['prefix' => 'featured-websites', 'as' => 'featured-website.'], function () {
            Route::resource('', 'FeaturedWebsiteController')->parameters(['' => 'featured-website']);
            Route::delete('items/destroy', [
                'as'         => 'deletes',
                'uses'       => 'FeaturedWebsiteController@deletes',
                'permission' => 'featured-website.destroy',
            ]);
            Route::post('update-order-by', [
                'as' => 'update-order-by',
                'uses' => 'FeaturedWebsiteController@postUpdateOrderby',
                'permission' => 'featured-website.edit',
            ]);
        });

        Route::group(['prefix' => 'featured-website-categories', 'as' => 'featured-website-category.'], function () {
            Route::resource('', 'FeaturedWebsiteCategoryController')->parameters(['' => 'featured-website-category']);
            Route::delete('items/destroy', [
                'as'         => 'deletes',
                'uses'       => 'FeaturedWebsiteCategoryController@deletes',
                'permission' => 'featured-website-category.destroy',
            ]);
        });
    });

    if (defined('THEME_MODULE_SCREEN_NAME')) {
        Route::group(apply_filters(BASE_FILTER_GROUP_PUBLIC_ROUTE, []), function () {

            if (SlugHelper::getPrefix(FeaturedWebsite::class)) {
                Route::get(SlugHelper::getPrefix(FeaturedWebsite::class) . '/{slug}', [
                    'uses' => 'PublicController@getFeaturedWebsite',
                ]);
            }

        });
    }

});
