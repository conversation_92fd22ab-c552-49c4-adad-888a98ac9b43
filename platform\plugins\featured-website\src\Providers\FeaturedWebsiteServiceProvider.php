<?php

namespace Shaqi\FeaturedWebsite\Providers;

use Shaqi\FeaturedWebsite\Models\FeaturedWebsite;
use Shaqi\FeaturedWebsite\Models\FeaturedWebsiteCategory;
use Illuminate\Support\ServiceProvider;
use Shaqi\FeaturedWebsite\Repositories\Caches\FeaturedWebsiteCacheDecorator;
use Shaqi\FeaturedWebsite\Repositories\Caches\FeaturedWebsiteCategoryCacheDecorator;
use Shaqi\FeaturedWebsite\Repositories\Eloquent\FeaturedWebsiteRepository;
use Shaqi\FeaturedWebsite\Repositories\Eloquent\FeaturedWebsiteCategoryRepository;
use Shaqi\FeaturedWebsite\Repositories\Interfaces\FeaturedWebsiteInterface;
use Shaqi\FeaturedWebsite\Repositories\Interfaces\FeaturedWebsiteCategoryInterface;
use Illuminate\Support\Facades\Event;
use Shaqi\Base\Traits\LoadAndPublishDataTrait;
use Illuminate\Routing\Events\RouteMatched;
use <PERSON>haqi\Theme\Events\RenderingSiteMapEvent;
use SlugHelper;
use SeoHelper;
use Gallery;
use SiteMapManager;

class FeaturedWebsiteServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register()
    {
        $this->app->bind(FeaturedWebsiteInterface::class, function () {
            return new FeaturedWebsiteCacheDecorator(new FeaturedWebsiteRepository(new FeaturedWebsite));
        });

        $this->app->bind(FeaturedWebsiteCategoryInterface::class, function () {
            return new FeaturedWebsiteCategoryCacheDecorator(new FeaturedWebsiteCategoryRepository(new FeaturedWebsiteCategory));
        });

        $this->setNamespace('plugins/featured-website')->loadHelpers();
    }

    public function boot()
    {
        // SlugHelper::registerModule(FeaturedWebsite::class, 'FeaturedWebsite');
        // SlugHelper::setPrefix(FeaturedWebsite::class, null, true);

        $this
            ->loadAndPublishConfigurations(['permissions'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes(['web']);


        $this->app->booted(function () {
            if (defined('CUSTOM_FIELD_MODULE_SCREEN_NAME')) {
                \CustomField::registerModule(FeaturedWebsite::class)
                    ->registerRule('basic', __('FeaturedWebsite'), FeaturedWebsite::class, function () {
                        return $this->app->make(FeaturedWebsiteInterface::class)->pluck('name', 'id');
                    })
                    ->expandRule('other', 'Model', 'model_name', function () {
                        return [
                            FeaturedWebsite::class => __('FeaturedWebsite'),
                        ];
                    });
            }

            // SeoHelper::registerModule([FeaturedWebsite::class]);
            // Gallery::registerModule([FeaturedWebsite::class]);

            // $this->app->register(HookServiceProvider::class);
        });

        Event::listen(RouteMatched::class, function () {
            dashboard_menu()->registerItem([
                'id'          => 'cms-plugins-featured-website',
                'priority'    => 5,
                'parent_id'   => null,
                'name'        => 'plugins/featured-website::featured-website.name',
                'icon'        => 'fa fa-globe',
                'permissions' => ['featured-website.index'],
            ])
            ->registerItem([
                'id'          => 'cms-plugins-featured-website-all',
                'priority'    => 5,
                'parent_id'   => 'cms-plugins-featured-website',
                'name'        => 'plugins/featured-website::featured-website.name',
                'icon'        => 'fa fa-globe',
                'url'         => route('featured-website.index'),
                'permissions' => ['featured-website.index'],
            ])
            ->registerItem([
                'id'          => 'cms-plugins-featured-website-categories',
                'priority'    => 1,
                'parent_id'   => 'cms-plugins-featured-website',
                'name'        => 'Categories',
                'icon'        => 'fa fa-tags',
                'url'         => route('featured-website-category.index'),
                'permissions' => ['featured-website-category.index'],
            ]);
        });

        // Event::listen(RenderingSiteMapEvent::class, function () {
        //     //\SiteMapManager::add('your-url', '2020-04-03 00:00:00', '0.8', 'monthly');
        //     // Add many URLs
        //     $featuredwebsite= FeaturedWebsite::All();
        //     foreach ($featuredwebsite as $item) {
        //         SiteMapManager::add($item->url, $item->updated_at, '0.8', 'daily');
        //     }
        // });
    }
}
