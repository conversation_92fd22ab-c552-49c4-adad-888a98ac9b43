<div class="container-fluid featuredWebsitesAddSec1">
    <div class="row">
		<div class="col-md-12">
			<div class="featuredWebsitesAddSec1Filters">
				<ul>
                    <li><a id="index-0" class="loadWebsite all active" type="button" > All</a></li>
                    @foreach(get_all_featured_website_categories() as $category)
                    <li><a id="index-{{ $loop->iteration }}" class="loadWebsite {{ Str::slug($category->name) }}" type="button" > {{ $category->name }}</a></li>
                    @endforeach
                    <!--<li><a id="index-2" class="loadWebsite the-vitals" type="button" > The Vitals</a></li>-->
                    <!--<li><a id="index-3" class="loadWebsite optimal" type="button" > Optimal</a></li>-->
                    <!--<li><a id="index-4" class="loadWebsite property-website" type="button" > Property Website</a></li>-->
				</ul>
			</div>
		</div>
	</div>

	<div class="row pb-5 featuredWebsitesAddSec1Grid">

        @php
        $featured_websites = get_all_featured_websites();
        @endphp
        @foreach ($featured_websites as $website)

        @php
        $website_logo = MetaBox::getMetaData($website, 'website_logo', true);
        $website_tag= MetaBox::getMetaData($website, 'website_tag', true);
        $website_button_link= MetaBox::getMetaData($website, 'website_button_link', true);
        @endphp

        <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12 featuredWebsiteGridItem
        @if($website->categories->isNotEmpty())
            {{ $website->categories->pluck('name')->map(fn($name) => Str::slug($name))->join(', ') }}
        @endif
        ">
            <div class="inner-box">
            	<div class="image-box">
            		<figure class="image">
            			<a href="#" data-discover="true">
            				<img src="{{ RvMedia::getImageUrl($website->image)  }}" alt="">
            			</a>
            		</figure>
            		<a class="icon" href="{{ $website_button_link }}" data-discover="true" target="_blank">
            			<i class="fa-solid fa-arrow-right-long"></i>
            		</a>
            	</div>
            	<div class="content-box">
            	     Total: {{ $website->categories->count() }} <br>
            	    <h5>
            	     @if($website->categories->isNotEmpty())
                        {{ $website->categories->pluck('name')->map(fn($name) => $name)->join(', ') }}
                    @endif
                    </h5>
            		<h4>{{ $website->name }}</h4>
            	</div>
            </div>
        </div>
        @endforeach
    </div>
</div>



            <!--<a href="{{ $website_button_link }}" target="_blank">-->
            <!--    <div class="featuredWebsitesAddSec1Col">-->
            <!--        <img src="{{ RvMedia::getImageUrl($website->image)  }}" alt="{{ $website->name }}">-->
            <!--        <h4 class="type">{{ $website_tag }}</h4>-->
            <!--    </div>-->
            <!--</a>-->


<!--<div class="featuredWebsitesAddSec1Inner">-->
                        <!--<img src="{{ RvMedia::getImageUrl($website_logo) }}" alt="{{ $website->name }}" class="portfolioLogo">-->
<!--                        <ul>-->
<!--                            <li><i class="fas fa-link" aria-hidden="true"></i></li>-->
<!--                        </ul>-->
<!--                    </div>-->
