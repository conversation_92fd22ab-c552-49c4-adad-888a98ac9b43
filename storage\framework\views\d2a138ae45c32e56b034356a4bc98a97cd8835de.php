<?php if (! $__env->hasRenderedOnce('2ec25f4e-1a4d-47a0-8930-ab304732e4ed')): $__env->markAsRenderedOnce('2ec25f4e-1a4d-47a0-8930-ab304732e4ed'); ?>
    <li class="dropdown dropdown-extended dropdown-inbox">
        <a href="<?php echo e(route('notifications.get-notification')); ?>" data-href="<?php echo e(route('notifications.update-notifications-count')); ?>" id="open-notification" class="dropdown-toggle dropdown-header-name">
            <input type="hidden" value="1" class="current-page">
            <i class="fas fa-bell"></i>
            <?php if($countNotificationUnread): ?>
                <span class="badge badge-default"> <?php echo e($countNotificationUnread); ?> </span>
            <?php endif; ?>
        </a>
    </li>
<?php endif; ?>

<?php /**PATH D:\laragon\www\focusedcre\platform/core/base/resources/views/notification/notification.blade.php ENDPATH**/ ?>