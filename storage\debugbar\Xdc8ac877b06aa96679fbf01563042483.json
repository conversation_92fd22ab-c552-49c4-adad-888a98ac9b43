{"__meta": {"id": "Xdc8ac877b06aa96679fbf01563042483", "datetime": "2025-07-31 12:05:09", "utime": 1753963509.151031, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.1.4", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963501.124282, "end": 1753963509.151052, "duration": 8.026770114898682, "duration_str": "8.03s", "measures": [{"label": "Booting", "start": 1753963501.124282, "relative_start": 0, "end": 1753963504.325245, "relative_end": 1753963504.325245, "duration": 3.200963020324707, "duration_str": "3.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753963504.451508, "relative_start": 3.327226161956787, "end": 1753963509.151055, "relative_end": 3.0994415283203125e-06, "duration": 4.699547052383423, "duration_str": "4.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43734088, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 21, "templates": [{"name": "theme.focusedcre::views.page (\\platform\\themes\\focusedcre\\views\\page.blade.php)", "param_count": 1, "params": ["page"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/views/page.blade.php:0"}, {"name": "theme.focusedcre::layouts.homepage (\\platform\\themes\\focusedcre\\layouts\\homepage.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/layouts/homepage.blade.php:0"}, {"name": "theme.focusedcre::partials.header (\\platform\\themes\\focusedcre\\partials\\header.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/header.blade.php:0"}, {"name": "packages/theme::partials.header (\\platform\\packages\\theme\\resources\\views\\partials\\header.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/packages/theme/resources/views/partials/header.blade.php:0"}, {"name": "plugins/captcha::header-meta (\\platform\\plugins\\captcha\\resources\\views\\header-meta.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/captcha/resources/views/header-meta.blade.php:0"}, {"name": "theme.focusedcre::partials.preloader (\\platform\\themes\\focusedcre\\partials\\preloader.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/preloader.blade.php:0"}, {"name": "theme.focusedcre::partials.menu (\\platform\\themes\\focusedcre\\partials\\menu.blade.php)", "param_count": 3, "params": ["menu", "menu_nodes", "options"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/menu.blade.php:0"}, {"name": "theme.focusedcre::partials.menu (\\platform\\themes\\focusedcre\\partials\\menu.blade.php)", "param_count": 3, "params": ["menu", "menu_nodes", "options"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/menu.blade.php:0"}, {"name": "theme.focusedcre::partials.menu (\\platform\\themes\\focusedcre\\partials\\menu.blade.php)", "param_count": 3, "params": ["menu", "menu_nodes", "options"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/menu.blade.php:0"}, {"name": "theme.focusedcre::partials.menu (\\platform\\themes\\focusedcre\\partials\\menu.blade.php)", "param_count": 3, "params": ["menu", "menu_nodes", "options"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/menu.blade.php:0"}, {"name": "theme.focusedcre::partials.menu (\\platform\\themes\\focusedcre\\partials\\menu.blade.php)", "param_count": 3, "params": ["menu", "menu_nodes", "options"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/menu.blade.php:0"}, {"name": "theme.focusedcre::partials.menu (\\platform\\themes\\focusedcre\\partials\\menu.blade.php)", "param_count": 3, "params": ["menu", "menu_nodes", "options"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/menu.blade.php:0"}, {"name": "theme.focusedcre::partials.shortcodes.clients-logo (\\platform\\themes\\focusedcre\\partials\\shortcodes\\clients-logo.blade.php)", "param_count": 2, "params": ["shortcode", "page"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/shortcodes/clients-logo.blade.php:0"}, {"name": "theme.focusedcre::partials.contact-form (\\platform\\themes\\focusedcre\\partials\\contact-form.blade.php)", "param_count": 1, "params": ["shortcode"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/contact-form.blade.php:0"}, {"name": "plugins/captcha::v3.html (\\platform\\plugins\\captcha\\resources\\views\\v3\\html.blade.php)", "param_count": 2, "params": ["name", "uniqueId"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/captcha/resources/views/v3/html.blade.php:0"}, {"name": "theme.focusedcre::partials.footer (\\platform\\themes\\focusedcre\\partials\\footer.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/footer.blade.php:0"}, {"name": "theme.focusedcre::partials.ecommerce.quick-view-modal (\\platform\\themes\\focusedcre\\partials\\ecommerce\\quick-view-modal.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/ecommerce/quick-view-modal.blade.php:0"}, {"name": "packages/theme::partials.footer (\\platform\\packages\\theme\\resources\\views\\partials\\footer.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/packages/theme/resources/views/partials/footer.blade.php:0"}, {"name": "packages/theme::admin-bar (\\platform\\packages\\theme\\resources\\views\\admin-bar.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/packages/theme/resources/views/admin-bar.blade.php:0"}, {"name": "plugins/captcha::v3.script (\\platform\\plugins\\captcha\\resources\\views\\v3\\script.blade.php)", "param_count": 5, "params": ["siteKey", "id", "action", "url", "isRendered"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/captcha/resources/views/v3/script.blade.php:0"}, {"name": "theme.focusedcre::partials.facebook-integration (\\platform\\themes\\focusedcre\\partials\\facebook-integration.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/themes/focusedcre/partials/facebook-integration.blade.php:0"}]}, "route": {"uri": "GET /", "middleware": "web, core", "as": "public.index", "controller": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getIndex", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php:21\">\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php:21-49</a>"}, "queries": {"nb_statements": 25, "nb_failed_statements": 0, "accumulated_duration": 0.14552, "accumulated_duration_str": "146ms", "statements": [{"sql": "select * from `users` where `id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 197}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 167}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "duration": 0.01563, "duration_str": "15.63ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:75", "connection": "focusedcre", "start_percent": 0, "width_percent": 10.741}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `updated_at`, `created_at`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'to the system', 'logged in', 2, 0, 2, 'Admin ..', 'info', '2025-07-31 12:05:07', '2025-07-31 12:05:07')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "to the system", "logged in", "2", "0", "2", "Admin ..", "info", "2025-07-31 12:05:07", "2025-07-31 12:05:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\platform\\plugins\\audit-log\\src\\Listeners\\LoginListener.php", "line": 33}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 745}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 172}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "duration": 0.03196, "duration_str": "31.96ms", "stmt_id": "\\platform\\plugins\\audit-log\\src\\Listeners\\LoginListener.php:33", "connection": "focusedcre", "start_percent": 10.741, "width_percent": 21.963}, {"sql": "select * from `slugs` where `reference_type` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `reference_id` = '24' and `prefix` = '' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\slug\\src\\SlugHelper.php", "line": 131}, {"index": 21, "namespace": null, "name": "\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 26}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.03333, "duration_str": "33.33ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 32.703, "width_percent": 22.904}, {"sql": "select * from `pages` where `id` = 24 and `status` = 'published' limit 1", "type": "query", "params": [], "bindings": ["24", "published"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 39}, {"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 29}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00718, "duration_str": "7.18ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 55.607, "width_percent": 4.934}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (24) and `slugs`.`reference_type` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 23, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 24, "namespace": null, "name": "\\vendor\\shaqi\\page\\src\\Services\\PageService.php", "line": 39}, {"index": 25, "namespace": null, "name": "\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 29}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 60.542, "width_percent": 0.474}, {"sql": "select * from `menus` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 123}, {"index": 17, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 120}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 210}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 195}, {"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 163}], "duration": 0.0076100000000000004, "duration_str": "7.61ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:123", "connection": "focusedcre", "start_percent": 61.016, "width_percent": 5.23}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`menu_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 123}, {"index": 22, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 120}, {"index": 23, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 210}, {"index": 24, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 163}], "duration": 0.00535, "duration_str": "5.35ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:123", "connection": "focusedcre", "start_percent": 66.245, "width_percent": 3.676}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` in (31, 129, 135, 136, 137, 138, 139, 140, 160, 162, 163, 164, 165, 167, 168, 170, 188, 189, 190, 192, 194, 195, 196, 197, 198, 199, 201, 202, 205) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 123}, {"index": 27, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 120}, {"index": 28, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 210}, {"index": 29, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 195}, {"index": 30, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 163}], "duration": 0.00276, "duration_str": "2.76ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:123", "connection": "focusedcre", "start_percent": 69.922, "width_percent": 1.897}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (135, 136, 137, 139, 140, 162, 163, 165, 167, 168, 170, 188, 189, 190, 195, 197, 199, 201, 202, 205) and `meta_boxes`.`reference_type` = 'Shaqi\\Menu\\Models\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": false, "backtrace": [{"index": 29, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 123}, {"index": 32, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 120}, {"index": 33, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 210}, {"index": 34, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 195}, {"index": 35, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 163}], "duration": 0.0069500000000000004, "duration_str": "6.95ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:123", "connection": "focusedcre", "start_percent": 71.818, "width_percent": 4.776}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (31, 129, 135, 136, 137, 138, 139, 140, 160, 162, 163, 164, 165, 167, 168, 170, 188, 189, 190, 192, 194, 195, 196, 197, 198, 199, 201, 202, 205) and `meta_boxes`.`reference_type` = '<PERSON>haqi\\Menu\\Models\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 123}, {"index": 27, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 120}, {"index": 28, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 210}, {"index": 29, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 195}, {"index": 30, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 163}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:123", "connection": "focusedcre", "start_percent": 76.594, "width_percent": 0.941}, {"sql": "select * from `menu_locations` where `menu_locations`.`menu_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 123}, {"index": 22, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 120}, {"index": 23, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 210}, {"index": 24, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 163}], "duration": 0.00571, "duration_str": "5.71ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:123", "connection": "focusedcre", "start_percent": 77.536, "width_percent": 3.924}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (1) and `meta_boxes`.`reference_type` = 'Shaqi\\Menu\\Models\\Menu'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\Menu"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 123}, {"index": 22, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 120}, {"index": 23, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 210}, {"index": 24, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\shaqi\\menu\\src\\Menu.php", "line": 163}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:123", "connection": "focusedcre", "start_percent": 81.46, "width_percent": 0.955}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'cre_website_development_icon' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "cre_website_development_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.0075899999999999995, "duration_str": "7.59ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 82.415, "width_percent": 5.216}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'cre_website_development_title' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "cre_website_development_title"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 87.631, "width_percent": 0.495}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'cre_website_development_text' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "cre_website_development_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00109, "duration_str": "1.09ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 88.125, "width_percent": 0.749}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'cre_marketing_solutions_icon' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "cre_marketing_solutions_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 88.874, "width_percent": 1.051}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'cre_marketing_solutions_title' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "cre_marketing_solutions_title"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 89.926, "width_percent": 0.632}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'cre_marketing_solutions_text' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "cre_marketing_solutions_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 90.558, "width_percent": 0.728}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'plans_pricing_icon' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "plans_pricing_icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 91.286, "width_percent": 0.68}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'plans_pricing_title' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "plans_pricing_title"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 91.967, "width_percent": 0.639}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'plans_pricing_text' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "plans_pricing_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 92.606, "width_percent": 0.632}, {"sql": "select * from `pages` where `id` = 24 limit 1", "type": "query", "params": [], "bindings": ["24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 81}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 100}, {"index": 19, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\shortcodes.php", "line": 57}, {"index": 24, "namespace": null, "name": "\\vendor\\shaqi\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 100}, {"index": 25, "namespace": null, "name": "\\vendor\\shaqi\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 80}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:81", "connection": "focusedcre", "start_percent": 93.238, "width_percent": 0.502}, {"sql": "select `images` from `gallery_meta` where `reference_id` = 24 and `reference_type` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' limit 1", "type": "query", "params": [], "bindings": ["24", "Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\gallery\\helpers\\helpers.php", "line": 17}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "duration": 0.00454, "duration_str": "4.54ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 93.74, "width_percent": 3.12}, {"sql": "select * from `custom_fields` where `use_for` = '<PERSON><PERSON><PERSON>\\Page\\Models\\Page' and `use_for_id` = 24 and `slug` = 'who_we_are' limit 1", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page", "24", "who_we_are"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\custom-field\\src\\Support\\CustomFieldSupport.php", "line": 534}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\custom-field\\helpers\\front.php", "line": 15}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 96.86, "width_percent": 0.529}, {"sql": "select * from `testimonials` where `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 370}, {"index": 17, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 18, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\functions.php", "line": 505}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "duration": 0.0038, "duration_str": "3.8ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:370", "connection": "focusedcre", "start_percent": 97.389, "width_percent": 2.611}]}, "models": {"data": {"Shaqi\\Testimonial\\Models\\Testimonial": 11, "Shaqi\\Gallery\\Models\\GalleryMeta": 1, "Shaqi\\CustomField\\Models\\CustomField": 10, "Shaqi\\Menu\\Models\\MenuLocation": 60, "Shaqi\\Menu\\Models\\MenuNode": 49, "Shaqi\\Menu\\Models\\Menu": 1, "Shaqi\\Page\\Models\\Page": 2, "Shaqi\\Slug\\Models\\Slug": 2, "Shaqi\\ACL\\Models\\User": 1}, "count": 137}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"https://focusedcre.local\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1611098404 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1611098404\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-592449137 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-592449137\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1137545023 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1137545023\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1876448185 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">http://localhost/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1920 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1747920257$o12$g1$t1747920296$j0$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1876448185\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:45</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"17 characters\">http://localhost/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1920 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1747920257$o12$g1$t1747920296$j0$l0$h0</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1315 characters\">C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\ProgramData\\ComposerSetup\\bin;C:\\Program Files\\Java\\jdk-15.0.2;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Amazon\\AWSCLIV2\\;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\laragon\\bin\\composer;D:\\laragon\\bin\\git\\bin;D:\\laragon\\bin\\git\\cmd;D:\\laragon\\bin\\git\\mingw64\\bin;D:\\laragon\\bin\\git\\usr\\bin;D:\\laragon\\bin\\mongodb\\mongodb-4.0.3\\;D:\\laragon\\bin\\mysql\\mariadb-10.6.7-winx64\\bin;D:\\laragon\\bin\\ngrok;D:\\laragon\\bin\\nodejs\\node-v16.16.0;D:\\laragon\\bin\\php\\php-8.1.4-Win32-vs16-x64;D:\\laragon\\bin\\postgresql\\postgresql\\bin;D:\\laragon\\bin\\python\\python-3.13;D:\\laragon\\bin\\python\\python-3.13\\Scripts;D:\\laragon\\bin\\yarn\\bin;D:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.47 (Win64) OpenSSL/1.1.1m PHP/8.1.4</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">D:/laragon/www/focusedcre/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">23174</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753963501.1243</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753963501</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2068808620 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">276c8eb96ecbc99bdaa7c37095beb04735960841</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;footprint&quot;:&quot;276c8eb96ecbc99bdaa7c37095beb04735960841&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;focusedcre.local&quot;,&quot;landing_page&quot;:&quot;new-website-request&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|6LfPDjUyZLXD0DhNLxRR7WMt2NSfbdUnceBmo2YcfbIcDul5oc1icuy6Y0d2|$2y$10$v3mwe1ya6fDmDwB5vikHTuie26Jjsc.ZMD/k9JtQCyVybjxTgiKCu</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_94P3KEDHFG</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068808620\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:05:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">6.7.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">Yes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNJYzN3NzIrb28xSjEzVUtrVmNsU3c9PSIsInZhbHVlIjoiNElkQXk2aldDUzVKSW5BcDZHeTNWWXRtejNMeW5FUjFQcUhCSDFpNDJ1RW5oTGY4NnZyQ1lzMDNlK2F1NEpvZXlnbzh5aUJXYXYvUjZTWDJPb0lHc2pxT09IWnFYNHVEZ0pzOUFVRlpGejNBendWSmFvYytkYXdWQVJvM0JPR2MiLCJtYWMiOiIyNDRkZDIzOWIwY2Q1NWMzMDc5MTAyMGUzZGViZDc3YjhlZDIwYWNhYjI0N2Q3M2I0MmJiNjAwMGNlNTNiOTc5IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:05:08 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shaqi_session=eyJpdiI6InJoU1FOZmYxVTVVRnIxL1JEQStHd2c9PSIsInZhbHVlIjoid2laT1gvWHNqd0c2L3ZVcXRKWm84RVRlUVJ1VTQyV1YwVzBCcXJDSFphck1BTTZvRXJIQ2ptOU9aSVRnVlBqRjRuY1VzYkNpRDFrQkJpd3I5TDhEbmVsVmVaaWNhSkMyZkxOS1ZJbXIwakVrK3ErcjUzSDQ4V2RPRFFwMXdKLzgiLCJtYWMiOiIzOTk2NmFlMzQwZGJlNDBkMmJjNGRjOTg4MmQyOTk4NTQwYjU2N2M5MjhhY2U5MDliNzc1NzIzYTgyOTRlY2EyIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:05:08 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNJYzN3NzIrb28xSjEzVUtrVmNsU3c9PSIsInZhbHVlIjoiNElkQXk2aldDUzVKSW5BcDZHeTNWWXRtejNMeW5FUjFQcUhCSDFpNDJ1RW5oTGY4NnZyQ1lzMDNlK2F1NEpvZXlnbzh5aUJXYXYvUjZTWDJPb0lHc2pxT09IWnFYNHVEZ0pzOUFVRlpGejNBendWSmFvYytkYXdWQVJvM0JPR2MiLCJtYWMiOiIyNDRkZDIzOWIwY2Q1NWMzMDc5MTAyMGUzZGViZDc3YjhlZDIwYWNhYjI0N2Q3M2I0MmJiNjAwMGNlNTNiOTc5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:05:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">shaqi_session=eyJpdiI6InJoU1FOZmYxVTVVRnIxL1JEQStHd2c9PSIsInZhbHVlIjoid2laT1gvWHNqd0c2L3ZVcXRKWm84RVRlUVJ1VTQyV1YwVzBCcXJDSFphck1BTTZvRXJIQ2ptOU9aSVRnVlBqRjRuY1VzYkNpRDFrQkJpd3I5TDhEbmVsVmVaaWNhSkMyZkxOS1ZJbXIwakVrK3ErcjUzSDQ4V2RPRFFwMXdKLzgiLCJtYWMiOiIzOTk2NmFlMzQwZGJlNDBkMmJjNGRjOTg4MmQyOTk4NTQwYjU2N2M5MjhhY2U5MDliNzc1NzIzYTgyOTRlY2EyIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:05:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-353190526 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"24 characters\">https://focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353190526\", {\"maxDepth\":0})</script>\n"}}