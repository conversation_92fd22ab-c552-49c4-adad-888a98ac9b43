{"__meta": {"id": "Xbd7b067dbe15f480cebc7dde7c70ee78", "datetime": "2025-07-31 12:37:40", "utime": 1753965460.253477, "method": "POST", "uri": "/admin/featured-websites/edit/112", "ip": "127.0.0.1"}, "php": {"version": "8.1.4", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753965457.864733, "end": 1753965460.253507, "duration": 2.3887739181518555, "duration_str": "2.39s", "measures": [{"label": "Booting", "start": 1753965457.864733, "relative_start": 0, "end": 1753965459.562027, "relative_end": 1753965459.562027, "duration": 1.697293996810913, "duration_str": "1.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753965459.563568, "relative_start": 1.6988351345062256, "end": 1753965460.25351, "relative_end": 3.0994415283203125e-06, "duration": 0.6899418830871582, "duration_str": "690ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 44392920, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/featured-websites/edit/{featured_website}", "middleware": "web, core, auth", "as": "featured-website.edit.update", "controller": "Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController@update", "namespace": "Shaqi\\FeaturedWebsite\\Http\\Controllers", "prefix": "admin/featured-websites", "where": [], "file": "<a href=\"vscode://file/D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:106\">\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:106-123</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.05577, "accumulated_duration_str": "55.77ms", "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 12}], "duration": 0.0060999999999999995, "duration_str": "6.1ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "focusedcre", "start_percent": 0, "width_percent": 10.938}, {"sql": "select count(*) as aggregate from `featured_website_categories` where `id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:54", "connection": "focusedcre", "start_percent": 10.938, "width_percent": 2.188}, {"sql": "select count(*) as aggregate from `featured_website_categories` where `id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:54", "connection": "focusedcre", "start_percent": 13.125, "width_percent": 2.367}, {"sql": "select count(*) as aggregate from `featured_website_categories` where `id` = '3'", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "duration": 0.00085, "duration_str": "850μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:54", "connection": "focusedcre", "start_percent": 15.492, "width_percent": 1.524}, {"sql": "select count(*) as aggregate from `featured_website_categories` where `id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:54", "connection": "focusedcre", "start_percent": 17.016, "width_percent": 1.345}, {"sql": "select * from `featured_websites` where `id` = '112' limit 1", "type": "query", "params": [], "bindings": ["112"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 81}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 100}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:81", "connection": "focusedcre", "start_percent": 18.361, "width_percent": 1.632}, {"sql": "update `featured_websites` set `status` = '{\\\"value\\\":\\\"published\\\",\\\"label\\\":\\\"Published\\\"}', `featured_websites`.`updated_at` = '2025-07-31 12:37:39' where `id` = 112", "type": "query", "params": [], "bindings": ["{&quot;value&quot;:&quot;published&quot;,&quot;label&quot;:&quot;Published&quot;}", "2025-07-31 12:37:39", "112"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 192}, {"index": 16, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 130}, {"index": 17, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 112}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.0062, "duration_str": "6.2ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:192", "connection": "focusedcre", "start_percent": 19.993, "width_percent": 11.117}, {"sql": "select * from `featured_website_category_featured_website` where `featured_website_category_featured_website`.`featured_website_id` = 112", "type": "query", "params": [], "bindings": ["112"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 115}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00157, "duration_str": "1.57ms", "stmt_id": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:115", "connection": "focusedcre", "start_percent": 31.11, "width_percent": 2.815}, {"sql": "insert into `featured_website_category_featured_website` (`category_id`, `featured_website_id`) values (1, 112)", "type": "query", "params": [], "bindings": ["1", "112"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.005730000000000001, "duration_str": "5.73ms", "stmt_id": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:115", "connection": "focusedcre", "start_percent": 33.925, "width_percent": 10.274}, {"sql": "insert into `featured_website_category_featured_website` (`category_id`, `featured_website_id`) values (2, 112)", "type": "query", "params": [], "bindings": ["2", "112"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.008199999999999999, "duration_str": "8.2ms", "stmt_id": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:115", "connection": "focusedcre", "start_percent": 44.199, "width_percent": 14.703}, {"sql": "insert into `featured_website_category_featured_website` (`category_id`, `featured_website_id`) values (3, 112)", "type": "query", "params": [], "bindings": ["3", "112"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00458, "duration_str": "4.58ms", "stmt_id": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:115", "connection": "focusedcre", "start_percent": 58.903, "width_percent": 8.212}, {"sql": "insert into `featured_website_category_featured_website` (`category_id`, `featured_website_id`) values (4, 112)", "type": "query", "params": [], "bindings": ["4", "112"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php", "line": 115}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.006690000000000001, "duration_str": "6.69ms", "stmt_id": "\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:115", "connection": "focusedcre", "start_percent": 67.115, "width_percent": 11.996}, {"sql": "select * from `meta_boxes` where `meta_key` = 'website_logo' and `reference_id` = 112 and `reference_type` = 'Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite' limit 1", "type": "query", "params": [], "bindings": ["website_logo", "112", "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 145}, {"index": 21, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\functions.php", "line": 488}, {"index": 25, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "duration": 0.00219, "duration_str": "2.19ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 79.111, "width_percent": 3.927}, {"sql": "select * from `meta_boxes` where `meta_key` = 'website_tag' and `reference_id` = 112 and `reference_type` = 'Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite' limit 1", "type": "query", "params": [], "bindings": ["website_tag", "112", "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 145}, {"index": 21, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\functions.php", "line": 489}, {"index": 25, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "duration": 0.0017, "duration_str": "1.7ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 83.037, "width_percent": 3.048}, {"sql": "select * from `meta_boxes` where `meta_key` = 'website_button_link' and `reference_id` = 112 and `reference_type` = 'Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite' limit 1", "type": "query", "params": [], "bindings": ["website_button_link", "112", "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 213}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 105}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Supports\\MetaBox.php", "line": 145}, {"index": 21, "namespace": null, "name": "\\platform\\themes\\focusedcre\\functions\\functions.php", "line": 490}, {"index": 25, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "duration": 0.00239, "duration_str": "2.39ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:213", "connection": "focusedcre", "start_percent": 86.086, "width_percent": 4.285}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'featured-website', 'updated', 2, 2, 112, 'Melnick', 'primary', '2025-07-31 12:37:40', '2025-07-31 12:37:40', '{\\\"name\\\":\\\"<PERSON><PERSON>\\\",\\\"website_tag\\\":\\\"Landing Page\\\",\\\"website_button_link\\\":\\\"https:\\/\\/melnickrea.com\\/\\\",\\\"content\\\":null,\\\"submit\\\":\\\"apply\\\",\\\"status\\\":\\\"published\\\",\\\"image\\\":\\\"featured-websites\\/melnick-1.jpg\\\",\\\"website_logo\\\":\\\"featured-websites\\/melnick.png\\\",\\\"categories\\\":[\\\"1\\\",\\\"2\\\",\\\"3\\\",\\\"4\\\"]}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "featured-website", "updated", "2", "2", "112", "<PERSON><PERSON>", "primary", "2025-07-31 12:37:40", "2025-07-31 12:37:40", "{&quot;name&quot;:&quot;<PERSON><PERSON>&quot;,&quot;website_tag&quot;:&quot;Landing Page&quot;,&quot;website_button_link&quot;:&quot;https:\\/\\/melnickrea.com\\/&quot;,&quot;content&quot;:null,&quot;submit&quot;:&quot;apply&quot;,&quot;status&quot;:&quot;published&quot;,&quot;image&quot;:&quot;featured-websites\\/melnick-1.jpg&quot;,&quot;website_logo&quot;:&quot;featured-websites\\/melnick.png&quot;,&quot;categories&quot;:[&quot;1&quot;,&quot;2&quot;,&quot;3&quot;,&quot;4&quot;]}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 56}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 451}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 451}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.00537, "duration_str": "5.37ms", "stmt_id": "\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php:56", "connection": "focusedcre", "start_percent": 90.371, "width_percent": 9.629}]}, "models": {"data": {"Shaqi\\Base\\Models\\MetaBox": 3, "Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite": 1, "Shaqi\\ACL\\Models\\User": 1}, "count": 5}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"https://focusedcre.local/admin/featured-websites/edit/112\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success_msg\"\n  ]\n  \"new\" => []\n]", "admin-theme": "default", "success_msg": "Updated successfully", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/featured-websites/edit/112", "status_code": "<pre class=sf-dump id=sf-dump-491991878 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-491991878\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-284200196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-284200196\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-494509182 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Melnick</span>\"\n  \"<span class=sf-dump-key>website_tag</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Landing Page</span>\"\n  \"<span class=sf-dump-key>website_button_link</span>\" => \"<span class=sf-dump-str title=\"23 characters\">https://melnickrea.com/</span>\"\n  \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>submit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"31 characters\">featured-websites/melnick-1.jpg</span>\"\n  \"<span class=sf-dump-key>website_logo</span>\" => \"<span class=sf-dump-str title=\"29 characters\">featured-websites/melnick.png</span>\"\n  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>2</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>3</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str>4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494509182\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1519787325 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">337</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">https://focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">https://focusedcre.local/admin/featured-websites/edit/112</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2668 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0; XSRF-TOKEN=eyJpdiI6ImI4T1hKdnhJV3pPQVlENEZBYzMzQlE9PSIsInZhbHVlIjoiOFZvNXlFUVVOdUFMRDUvOVQ3Qm54cGVHN2NyV0Q0NnFZbW1zYTc3WGlaK2FGd1lObGFSdkhpZGVPeHJ2a3FhTU5vbmxGa3E1aTNYTTY1L3pBdys5dmk1aXlKcW1wN0ZRalNwT2k0RU55QTQrZEZObmhuNCtuZTJ4SDl4TTJaU2oiLCJtYWMiOiJlYzljMmQ1ODEzZTRlN2E4NWU5MjZkNjc0MTc2Mzg0YWU0MjM0ZDQ3MWYxNjgyODA5NjA5NTE2YTUxMzFkNDEyIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IkNFdVVmb3hJbDdhYVQ5aGE3VDBNUGc9PSIsInZhbHVlIjoibU02MldBc2FPZ3d1NU13ZFErWDhEcnJ5V1dUd2lKQmxya0tmOU0rT1F4TE1JNXIrSUpyNTUyN2VxcGdQWnQ0VTJiWm1YQUdndkt3bzMrZDgwM25mQzZKUkFnbkVZWldHbUp5TVRBeFZiN1ZhZGFmdUpDUmcwMU8vM1k1YTMzaFIiLCJtYWMiOiJjYmYzMTMyN2IwMzA2NWUzMzlmYzI1MmZhNzYwMDVhM2UwYmZhZGYxMDk4YzIwM2YzMDUzMjA1OGMzYWNlMGQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1519787325\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:53</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">337</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"24 characters\">https://focusedcre.local</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"57 characters\">https://focusedcre.local/admin/featured-websites/edit/112</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2668 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0; XSRF-TOKEN=eyJpdiI6ImI4T1hKdnhJV3pPQVlENEZBYzMzQlE9PSIsInZhbHVlIjoiOFZvNXlFUVVOdUFMRDUvOVQ3Qm54cGVHN2NyV0Q0NnFZbW1zYTc3WGlaK2FGd1lObGFSdkhpZGVPeHJ2a3FhTU5vbmxGa3E1aTNYTTY1L3pBdys5dmk1aXlKcW1wN0ZRalNwT2k0RU55QTQrZEZObmhuNCtuZTJ4SDl4TTJaU2oiLCJtYWMiOiJlYzljMmQ1ODEzZTRlN2E4NWU5MjZkNjc0MTc2Mzg0YWU0MjM0ZDQ3MWYxNjgyODA5NjA5NTE2YTUxMzFkNDEyIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IkNFdVVmb3hJbDdhYVQ5aGE3VDBNUGc9PSIsInZhbHVlIjoibU02MldBc2FPZ3d1NU13ZFErWDhEcnJ5V1dUd2lKQmxya0tmOU0rT1F4TE1JNXIrSUpyNTUyN2VxcGdQWnQ0VTJiWm1YQUdndkt3bzMrZDgwM25mQzZKUkFnbkVZWldHbUp5TVRBeFZiN1ZhZGFmdUpDUmcwMU8vM1k1YTMzaFIiLCJtYWMiOiJjYmYzMTMyN2IwMzA2NWUzMzlmYzI1MmZhNzYwMDVhM2UwYmZhZGYxMDk4YzIwM2YzMDUzMjA1OGMzYWNlMGQ1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1315 characters\">C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\ProgramData\\ComposerSetup\\bin;C:\\Program Files\\Java\\jdk-15.0.2;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Amazon\\AWSCLIV2\\;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\laragon\\bin\\composer;D:\\laragon\\bin\\git\\bin;D:\\laragon\\bin\\git\\cmd;D:\\laragon\\bin\\git\\mingw64\\bin;D:\\laragon\\bin\\git\\usr\\bin;D:\\laragon\\bin\\mongodb\\mongodb-4.0.3\\;D:\\laragon\\bin\\mysql\\mariadb-10.6.7-winx64\\bin;D:\\laragon\\bin\\ngrok;D:\\laragon\\bin\\nodejs\\node-v16.16.0;D:\\laragon\\bin\\php\\php-8.1.4-Win32-vs16-x64;D:\\laragon\\bin\\postgresql\\postgresql\\bin;D:\\laragon\\bin\\python\\python-3.13;D:\\laragon\\bin\\python\\python-3.13\\Scripts;D:\\laragon\\bin\\yarn\\bin;D:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.47 (Win64) OpenSSL/1.1.1m PHP/8.1.4</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">D:/laragon/www/focusedcre/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">24757</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/admin/featured-websites/edit/112</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/admin/featured-websites/edit/112</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753965457.8647</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753965457</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-825648699 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">276c8eb96ecbc99bdaa7c37095beb04735960841</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;footprint&quot;:&quot;276c8eb96ecbc99bdaa7c37095beb04735960841&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;focusedcre.local&quot;,&quot;landing_page&quot;:&quot;new-website-request&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|6LfPDjUyZLXD0DhNLxRR7WMt2NSfbdUnceBmo2YcfbIcDul5oc1icuy6Y0d2|$2y$10$v3mwe1ya6fDmDwB5vikHTuie26Jjsc.ZMD/k9JtQCyVybjxTgiKCu</span>\"\n  \"<span class=sf-dump-key>_gid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_94P3KEDHFG</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">801BcPCBI6AAQOWuMaIoY4d3XHvjwOixQ6zrnhTO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825648699\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1325291149 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:37:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">https://focusedcre.local/admin/featured-websites/edit/112</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im5ucnQxcW4rc0FraUdhOTBWTGRTRmc9PSIsInZhbHVlIjoiRStxTDMyalBjU0d2R1lHZG84eUpGQVRNclNnWk9lN3VoQkFLSGh6d3UzRE9DSy9lSXV0T1pMY0ltb2ZGWXVlM3FFcEVocStFamsyRnZmR3dOaExNWUNCMjNwSGRJQWZXb0txb005R2hudGV0RUh5S2U5WXhHaWlsZktlNXB1b2UiLCJtYWMiOiI4MGI0ODc4MjNmNjE3OWY4MTc2NGQ2M2UwYmRhZTYyZTBmNmQyNDBkN2FlYTllODU0NDUwNTg4NmQyNDI5YzliIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:37:40 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shaqi_session=eyJpdiI6InFSejB4N0w5ajZ4UHVJU2dWV0VYeHc9PSIsInZhbHVlIjoiVDBpNldGWFAybnljclY2bWhsb3p5TlhuUGVvTjdwSXc4Qk9YSzAzWm5hK1EwZFRIWlNIL0JOdnFGMXMxNFJ0OU5sOWdwQWM1U3RyQithcmFGU2tTWHg5aXJIOTV2c1I3TEtKT3dNelVNcEVicjFnZEdMLzF6bFNSdEhCNUk1OEMiLCJtYWMiOiI3ZDY2MTU5MzQ4NzBhMmJkM2U0MWRiYmI3MzZiMDViZjUxYzFhY2NiNmNjM2QzYmUxODMxZjBhZTY5MTRiYTdlIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:37:40 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im5ucnQxcW4rc0FraUdhOTBWTGRTRmc9PSIsInZhbHVlIjoiRStxTDMyalBjU0d2R1lHZG84eUpGQVRNclNnWk9lN3VoQkFLSGh6d3UzRE9DSy9lSXV0T1pMY0ltb2ZGWXVlM3FFcEVocStFamsyRnZmR3dOaExNWUNCMjNwSGRJQWZXb0txb005R2hudGV0RUh5S2U5WXhHaWlsZktlNXB1b2UiLCJtYWMiOiI4MGI0ODc4MjNmNjE3OWY4MTc2NGQ2M2UwYmRhZTYyZTBmNmQyNDBkN2FlYTllODU0NDUwNTg4NmQyNDI5YzliIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:37:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">shaqi_session=eyJpdiI6InFSejB4N0w5ajZ4UHVJU2dWV0VYeHc9PSIsInZhbHVlIjoiVDBpNldGWFAybnljclY2bWhsb3p5TlhuUGVvTjdwSXc4Qk9YSzAzWm5hK1EwZFRIWlNIL0JOdnFGMXMxNFJ0OU5sOWdwQWM1U3RyQithcmFGU2tTWHg5aXJIOTV2c1I3TEtKT3dNelVNcEVicjFnZEdMLzF6bFNSdEhCNUk1OEMiLCJtYWMiOiI3ZDY2MTU5MzQ4NzBhMmJkM2U0MWRiYmI3MzZiMDViZjUxYzFhY2NiNmNjM2QzYmUxODMxZjBhZTY5MTRiYTdlIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:37:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325291149\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1096364040 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"57 characters\">https://focusedcre.local/admin/featured-websites/edit/112</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin-theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096364040\", {\"maxDepth\":0})</script>\n"}}