<?php

namespace Shaqi\FeaturedWebsite\Forms;

use <PERSON><PERSON><PERSON>\Base\Forms\FormAbstract;
use <PERSON><PERSON>qi\Base\Enums\BaseStatusEnum;
use <PERSON><PERSON>qi\FeaturedWebsite\Http\Requests\FeaturedWebsiteCategoryRequest;
use <PERSON><PERSON>qi\FeaturedWebsite\Models\FeaturedWebsiteCategory;

class FeaturedWebsiteCategoryForm extends FormAbstract
{

    /**
     * {@inheritDoc}
     */
    public function buildForm()
    {
        $this
            ->setupModel(new FeaturedWebsiteCategory)
            ->setValidatorClass(FeaturedWebsiteCategoryRequest::class)
            ->withCustomFields()
            ->add('name', 'text', [
                'label'      => trans('core/base::forms.name'),
                'label_attr' => ['class' => 'control-label required'],
                'attr'       => [
                    'placeholder'  => trans('core/base::forms.name_placeholder'),
                    'data-counter' => 120,
                ],
            ])
            ->add('status', 'customSelect', [
                'label'      => trans('core/base::tables.status'),
                'label_attr' => ['class' => 'control-label required'],
                'attr'       => [
                    'class' => 'form-control select-full',
                ],
                'choices'    => BaseStatusEnum::labels(),
            ])
            ->setBreakFieldPoint('status');
    }
}
