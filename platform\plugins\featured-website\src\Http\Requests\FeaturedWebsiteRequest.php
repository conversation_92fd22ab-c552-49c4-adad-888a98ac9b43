<?php

namespace Shaqi\FeaturedWebsite\Http\Requests;

use <PERSON>haqi\Base\Enums\BaseStatusEnum;
use Shaqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class FeaturedWebsiteRequest extends Request
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules():array
    {
        return [
            'name'       => 'required',
            'status'     => Rule::in(BaseStatusEnum::values()),
            'categories' => 'nullable|array',
            'categories.*' => 'exists:featured_website_categories,id',
        ];
    }
}
