{"__meta": {"id": "Xab1a3484c63fa2e20b9b9ac92dce9a56", "datetime": "2025-07-31 13:06:39", "utime": 1753967199.20038, "method": "POST", "uri": "/admin/featured-websites?", "ip": "127.0.0.1"}, "php": {"version": "8.1.4", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753967197.790384, "end": 1753967199.200407, "duration": 1.4100229740142822, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1753967197.790384, "relative_start": 0, "end": 1753967198.938312, "relative_end": 1753967198.938312, "duration": 1.14792799949646, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753967198.939114, "relative_start": 1.1487300395965576, "end": 1753967199.200411, "relative_end": 4.0531158447265625e-06, "duration": 0.26129698753356934, "duration_str": "261ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43767728, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 31, "templates": [{"name": "core/table::bulk-changes (\\platform\\core\\table\\resources\\views\\bulk-changes.blade.php)", "param_count": 3, "params": ["bulk_changes", "class", "url"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/bulk-changes.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}, {"name": "core/table::partials.actions (\\platform\\core\\table\\resources\\views\\partials\\actions.blade.php)", "param_count": 4, "params": ["edit", "delete", "item", "extra"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/actions.blade.php:0"}, {"name": "core/table::partials.checkbox (\\platform\\core\\table\\resources\\views\\partials\\checkbox.blade.php)", "param_count": 1, "params": ["id"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/table/resources/views/partials/checkbox.blade.php:0"}, {"name": "plugins/featured-website::partials.sort-order (\\platform\\plugins\\featured-website\\resources\\views\\partials\\sort-order.blade.php)", "param_count": 1, "params": ["item"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/featured-website/resources/views/partials/sort-order.blade.php:0"}]}, "route": {"uri": "GET admin/featured-websites", "middleware": "web, core, auth", "as": "featured-website.index", "controller": "Shaqi\\FeaturedWebsite\\Http\\Controllers\\FeaturedWebsiteController@index", "namespace": "Shaqi\\FeaturedWebsite\\Http\\Controllers", "prefix": "admin/featured-websites", "where": [], "file": "<a href=\"vscode://file/D:\\laragon\\www\\focusedcre\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:40\">\\platform\\plugins\\featured-website\\src\\Http\\Controllers\\FeaturedWebsiteController.php:40-48</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01808, "accumulated_duration_str": "18.08ms", "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 12}], "duration": 0.00587, "duration_str": "5.87ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "focusedcre", "start_percent": 0, "width_percent": 32.467}, {"sql": "select count(*) as aggregate from `featured_websites`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 205}, {"index": 16, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 848}, {"index": 17, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 186}, {"index": 18, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 858}], "duration": 0.00217, "duration_str": "2.17ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:205", "connection": "focusedcre", "start_percent": 32.467, "width_percent": 12.002}, {"sql": "select count(*) as aggregate from `featured_websites` where (LOWER(`featured_websites`.`id`) LIKE '%f%' or LOWER(`featured_websites`.`image`) LIKE '%f%' or LOWER(`featured_websites`.`name`) LIKE '%f%' or LOWER(`featured_websites`.`order`) LIKE '%f%' or LOWER(`featured_websites`.`created_at`) LIKE '%f%' or LOWER(`featured_websites`.`status`) LIKE '%f%')", "type": "query", "params": [], "bindings": ["%f%", "%f%", "%f%", "%f%", "%f%", "%f%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 205}, {"index": 16, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 858}, {"index": 17, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 302}, {"index": 18, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 188}, {"index": 19, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}], "duration": 0.00736, "duration_str": "7.36ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:205", "connection": "focusedcre", "start_percent": 44.469, "width_percent": 40.708}, {"sql": "select `id`, `image`, `name`, `created_at`, `status`, `order` from `featured_websites` where (LOWER(`featured_websites`.`id`) LIKE '%f%' or LOWER(`featured_websites`.`image`) LIKE '%f%' or LOWER(`featured_websites`.`name`) LIKE '%f%' or LOWER(`featured_websites`.`order`) LIKE '%f%' or LOWER(`featured_websites`.`created_at`) LIKE '%f%' or LOWER(`featured_websites`.`status`) LIKE '%f%') order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["%f%", "%f%", "%f%", "%f%", "%f%", "%f%"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 175}, {"index": 15, "namespace": null, "name": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 158}, {"index": 16, "namespace": null, "name": "\\vendor\\shaqi\\platform\\table\\src\\Abstracts\\TableAbstract.php", "line": 858}, {"index": 17, "namespace": null, "name": "\\platform\\plugins\\featured-website\\src\\Tables\\FeaturedWebsiteTable.php", "line": 78}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00268, "duration_str": "2.68ms", "stmt_id": "\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php:175", "connection": "focusedcre", "start_percent": 85.177, "width_percent": 14.823}]}, "models": {"data": {"Shaqi\\FeaturedWebsite\\Models\\FeaturedWebsite": 10, "Shaqi\\ACL\\Models\\User": 1}, "count": 11}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"https://focusedcre.local/admin/featured-websites\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "admin-theme": "default"}, "request": {"path_info": "/admin/featured-websites", "status_code": "<pre class=sf-dump id=sf-dump-269023903 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-269023903\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-902364983 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-902364983\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-718340600 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">operations</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">operations</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>f</span>\"\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718340600\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1957834743 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1874</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">https://focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">https://focusedcre.local/admin/featured-websites</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2668 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0; XSRF-TOKEN=eyJpdiI6IkhIYjhVU1NhMUlSbElSOFE5aEpab3c9PSIsInZhbHVlIjoiZWo2RHQ4RGpCeWkxME0wMFcyajUvTXo5YmQxOTA3NlhyL1FwWjlvYmFMY3JBKzBlK2hYa0lYNlNmbzRjL0s5Z1RTNjZkYUdBbHJvUnU1NUpEbDY2NnA5M2tha0hpakUzS2hhRW5PdEtxS3hFMjdqa1pRM0xYTTVxWWlFWTVLZEYiLCJtYWMiOiIyMmY2YTY0YmZlNjViYTk0NzU2MzViYWEyMDc3OTViM2MxNzM5ZjA5NjAxYTAwZjIxZDM1NmQ1MzA4YjI3MWUxIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6InNMbUY5RDVWcUVZWGpuTk5UMmwvNXc9PSIsInZhbHVlIjoiWllRWjRhUHFwWmlKd21qNWNuT2RzVGhIbW9TK0pkK3hyVzNGeS85cmVPK3F1UDVpcHNtU0VOMjg1WFgyYjV2emY0aUwzdCtIb1ZzUEJESkowaTY5UUQzYUUwOGdVZXJRSWJ4NUhVaCtoUXozQ0owVmJIRW1Cdk5MRVpuWGwrMjkiLCJtYWMiOiIzZWUyNWJkNzE5NGNkYzk5YjM1YmFkYjY3YzA2OGVmYzY3YzgzNDM1NGNiZDdjN2Y5YTdlZGFjODAwNzk3NTg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957834743\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-610604281 data-indent-pad=\"  \"><span class=sf-dump-note>array:53</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1874</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_X_REQUESTED_WITH</span>\" => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"24 characters\">https://focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"48 characters\">https://focusedcre.local/admin/featured-websites</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2668 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; _gid=GA1.2.1106596527.1753963514; _ga=GA1.1.1448235322.1747742727; _ga_94P3KEDHFG=GS2.1.s1753963514$o13$g1$t1753963533$j41$l0$h0; XSRF-TOKEN=eyJpdiI6IkhIYjhVU1NhMUlSbElSOFE5aEpab3c9PSIsInZhbHVlIjoiZWo2RHQ4RGpCeWkxME0wMFcyajUvTXo5YmQxOTA3NlhyL1FwWjlvYmFMY3JBKzBlK2hYa0lYNlNmbzRjL0s5Z1RTNjZkYUdBbHJvUnU1NUpEbDY2NnA5M2tha0hpakUzS2hhRW5PdEtxS3hFMjdqa1pRM0xYTTVxWWlFWTVLZEYiLCJtYWMiOiIyMmY2YTY0YmZlNjViYTk0NzU2MzViYWEyMDc3OTViM2MxNzM5ZjA5NjAxYTAwZjIxZDM1NmQ1MzA4YjI3MWUxIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6InNMbUY5RDVWcUVZWGpuTk5UMmwvNXc9PSIsInZhbHVlIjoiWllRWjRhUHFwWmlKd21qNWNuT2RzVGhIbW9TK0pkK3hyVzNGeS85cmVPK3F1UDVpcHNtU0VOMjg1WFgyYjV2emY0aUwzdCtIb1ZzUEJESkowaTY5UUQzYUUwOGdVZXJRSWJ4NUhVaCtoUXozQ0owVmJIRW1Cdk5MRVpuWGwrMjkiLCJtYWMiOiIzZWUyNWJkNzE5NGNkYzk5YjM1YmFkYjY3YzA2OGVmYzY3YzgzNDM1NGNiZDdjN2Y5YTdlZGFjODAwNzk3NTg2IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1315 characters\">C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\ProgramData\\ComposerSetup\\bin;C:\\Program Files\\Java\\jdk-15.0.2;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Amazon\\AWSCLIV2\\;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\laragon\\bin\\composer;D:\\laragon\\bin\\git\\bin;D:\\laragon\\bin\\git\\cmd;D:\\laragon\\bin\\git\\mingw64\\bin;D:\\laragon\\bin\\git\\usr\\bin;D:\\laragon\\bin\\mongodb\\mongodb-4.0.3\\;D:\\laragon\\bin\\mysql\\mariadb-10.6.7-winx64\\bin;D:\\laragon\\bin\\ngrok;D:\\laragon\\bin\\nodejs\\node-v16.16.0;D:\\laragon\\bin\\php\\php-8.1.4-Win32-vs16-x64;D:\\laragon\\bin\\postgresql\\postgresql\\bin;D:\\laragon\\bin\\python\\python-3.13;D:\\laragon\\bin\\python\\python-3.13\\Scripts;D:\\laragon\\bin\\yarn\\bin;D:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.47 (Win64) OpenSSL/1.1.1m PHP/8.1.4</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">D:/laragon/www/focusedcre/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">25525</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/admin/featured-websites</span>\"\n  \"<span class=sf-dump-key>REDIRECT_QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"25 characters\">/admin/featured-websites?</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753967197.7904</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753967197</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610604281\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1155330901 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">276c8eb96ecbc99bdaa7c37095beb04735960841</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;footprint&quot;:&quot;276c8eb96ecbc99bdaa7c37095beb04735960841&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;focusedcre.local&quot;,&quot;landing_page&quot;:&quot;new-website-request&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|6LfPDjUyZLXD0DhNLxRR7WMt2NSfbdUnceBmo2YcfbIcDul5oc1icuy6Y0d2|$2y$10$v3mwe1ya6fDmDwB5vikHTuie26Jjsc.ZMD/k9JtQCyVybjxTgiKCu</span>\"\n  \"<span class=sf-dump-key>_gid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_94P3KEDHFG</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">801BcPCBI6AAQOWuMaIoY4d3XHvjwOixQ6zrnhTO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155330901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-783278018 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 13:06:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik1iOGlRVmU4MVl3VHZxVlQycFQ4NVE9PSIsInZhbHVlIjoiNXdCWlBGTzBpVmVRYkozOFFwL2JSSXJpV0FKOGFsaGJWSmJ5N1JlRVFJQjF1dzEyTFJkZUg5dFh0c2JqR0tEUXo2S2JsbklKQmhWcWdQbHhFenllKzU1d0YzSXAreGRMd1RCaDFXaGdBYUdDeXJoWitEMGw2VzQrLyt5OFNPVDEiLCJtYWMiOiI5NGNhMWNlMDI4ZjA0N2YzZGEzNDEzY2I2N2JjYTlhZmQ1NTI2MjBlYWI3ODFhYTBiZjZiMzRmY2U2ZThkNDVjIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 15:06:39 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shaqi_session=eyJpdiI6IklkczkvVExVcVhXTlE3bHdFcHN4UGc9PSIsInZhbHVlIjoiM2FNK2Voa2lEVUdndmw1T0h1N0Z4M0FWOFJkVGlwM1lvMzllRkgwQ3RQamgxcnhZbGxTV0p6eUVCWXVLWU9qZmRORDVuaTFqajJRTDQ0cXVJUlpBeE8wRkxDcmlYY0UwcW9KYkc5OEhPcWZGTDk1OFFJaXlLcUdtQnAxbmwvOTIiLCJtYWMiOiI4NDg0OWQyMzAyYzJkYTFhOWM1ZDlhY2Y0Yzk1NzBlN2M1MjFlODRhNTQwZTZmMGUwZjkyOWEyMjk3N2IwOGVhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 15:06:39 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik1iOGlRVmU4MVl3VHZxVlQycFQ4NVE9PSIsInZhbHVlIjoiNXdCWlBGTzBpVmVRYkozOFFwL2JSSXJpV0FKOGFsaGJWSmJ5N1JlRVFJQjF1dzEyTFJkZUg5dFh0c2JqR0tEUXo2S2JsbklKQmhWcWdQbHhFenllKzU1d0YzSXAreGRMd1RCaDFXaGdBYUdDeXJoWitEMGw2VzQrLyt5OFNPVDEiLCJtYWMiOiI5NGNhMWNlMDI4ZjA0N2YzZGEzNDEzY2I2N2JjYTlhZmQ1NTI2MjBlYWI3ODFhYTBiZjZiMzRmY2U2ZThkNDVjIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 15:06:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">shaqi_session=eyJpdiI6IklkczkvVExVcVhXTlE3bHdFcHN4UGc9PSIsInZhbHVlIjoiM2FNK2Voa2lEVUdndmw1T0h1N0Z4M0FWOFJkVGlwM1lvMzllRkgwQ3RQamgxcnhZbGxTV0p6eUVCWXVLWU9qZmRORDVuaTFqajJRTDQ0cXVJUlpBeE8wRkxDcmlYY0UwcW9KYkc5OEhPcWZGTDk1OFFJaXlLcUdtQnAxbmwvOTIiLCJtYWMiOiI4NDg0OWQyMzAyYzJkYTFhOWM1ZDlhY2Y0Yzk1NzBlN2M1MjFlODRhNTQwZTZmMGUwZjkyOWEyMjk3N2IwOGVhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 15:06:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783278018\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1091200152 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"48 characters\">https://focusedcre.local/admin/featured-websites</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin-theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091200152\", {\"maxDepth\":0})</script>\n"}}