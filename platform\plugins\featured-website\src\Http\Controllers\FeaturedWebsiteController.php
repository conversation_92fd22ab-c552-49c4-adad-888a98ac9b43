<?php

namespace Shaqi\FeaturedWebsite\Http\Controllers;

use Shaqi\Base\Events\BeforeEditContentEvent;
use <PERSON><PERSON>qi\FeaturedWebsite\Http\Requests\FeaturedWebsiteRequest;
use <PERSON>haqi\FeaturedWebsite\Repositories\Interfaces\FeaturedWebsiteInterface;
use <PERSON><PERSON>qi\Base\Http\Controllers\BaseController;
use Illuminate\Http\Request;
use Exception;
use Shaqi\FeaturedWebsite\Tables\FeaturedWebsiteTable;
use Shaqi\Base\Events\CreatedContentEvent;
use Shaqi\Base\Events\DeletedContentEvent;
use Shaqi\Base\Events\UpdatedContentEvent;
use Shaqi\Base\Facades\Assets;
use Shaqi\Base\Http\Responses\BaseHttpResponse;
use Shaqi\FeaturedWebsite\Forms\FeaturedWebsiteForm;
use Shaqi\Base\Forms\FormBuilder;
use Shaqi\Theme\Asset;

class FeaturedWebsiteController extends BaseController
{
    /**
     * @var FeaturedWebsiteInterface
     */
    protected $featuredWebsiteRepository;

    /**
     * @param FeaturedWebsiteInterface $featuredWebsiteRepository
     */
    public function __construct(FeaturedWebsiteInterface $featuredWebsiteRepository)
    {
        $this->featuredWebsiteRepository = $featuredWebsiteRepository;
    }

    /**
     * @param FeaturedWebsiteTable $table
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index(FeaturedWebsiteTable $table)
    {
        page_title()->setTitle(trans('plugins/featured-website::featured-website.name'));

        Assets::addScripts(['bootstrap-editable'])
            ->addStyles(['bootstrap-editable']);

        return $table->renderTable();
    }

    /**
     * @param FormBuilder $formBuilder
     * @return string
     */
    public function create(FormBuilder $formBuilder)
    {
        page_title()->setTitle(trans('plugins/featured-website::featured-website.create'));

        return $formBuilder->create(FeaturedWebsiteForm::class)->renderForm();
    }

    /**
     * @param FeaturedWebsiteRequest $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     */
    public function store(FeaturedWebsiteRequest $request, BaseHttpResponse $response)
    {
        $featuredWebsite = $this->featuredWebsiteRepository->createOrUpdate($request->input());

        if ($featuredWebsite) {
            $featuredWebsite->categories()->sync($request->input('categories', []));
        }

        event(new CreatedContentEvent(FEATURED_WEBSITE_MODULE_SCREEN_NAME, $request, $featuredWebsite));

        return $response
            ->setPreviousUrl(route('featured-website.index'))
            ->setNextUrl(route('featured-website.edit', $featuredWebsite->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    /**
     * @param int $id
     * @param Request $request
     * @param FormBuilder $formBuilder
     * @return string
     */
    public function edit($id, FormBuilder $formBuilder, Request $request)
    {

        $featuredWebsite = $this->featuredWebsiteRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $featuredWebsite));

        page_title()->setTitle(trans('plugins/featured-website::featured-website.edit') . ' "' . $featuredWebsite->name . '"');

        return $formBuilder->create(FeaturedWebsiteForm::class, ['model' => $featuredWebsite])->renderForm();
    }

    /**
     * @param int $id
     * @param FeaturedWebsiteRequest $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     */
    public function update($id, FeaturedWebsiteRequest $request, BaseHttpResponse $response)
    {
        $featuredWebsite = $this->featuredWebsiteRepository->findOrFail($id);

        $featuredWebsite->fill($request->input());

        $featuredWebsite = $this->featuredWebsiteRepository->createOrUpdate($featuredWebsite);

        if ($featuredWebsite) {
            $featuredWebsite->categories()->sync($request->input('categories', []));
        }

        event(new UpdatedContentEvent(FEATURED_WEBSITE_MODULE_SCREEN_NAME, $request, $featuredWebsite));

        return $response
            ->setPreviousUrl(route('featured-website.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    /**
     * @param int $id
     * @param Request $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     */
    public function destroy(Request $request, $id, BaseHttpResponse $response)
    {
        try {
            $featuredWebsite = $this->featuredWebsiteRepository->findOrFail($id);

            $this->featuredWebsiteRepository->delete($featuredWebsite);

            event(new DeletedContentEvent(FEATURED_WEBSITE_MODULE_SCREEN_NAME, $request, $featuredWebsite));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     * @throws Exception
     */
    public function deletes(Request $request, BaseHttpResponse $response)
    {
        $ids = $request->input('ids');
        if (empty($ids)) {
            return $response
                ->setError()
                ->setMessage(trans('core/base::notices.no_select'));
        }

        foreach ($ids as $id) {
            $featuredWebsite = $this->featuredWebsiteRepository->findOrFail($id);
            $this->featuredWebsiteRepository->delete($featuredWebsite);
            event(new DeletedContentEvent(FEATURED_WEBSITE_MODULE_SCREEN_NAME, $request, $featuredWebsite));
        }

        return $response->setMessage(trans('core/base::notices.delete_success_message'));
    }

    public function postUpdateOrderBy(Request $request, BaseHttpResponse $response)
    {
        $featuredWebsite = $this->featuredWebsiteRepository->findOrFail($request->input('pk'));
        $featuredWebsite->order = $request->input('value', 0);
        $this->featuredWebsiteRepository->createOrUpdate($featuredWebsite);

        return $response->setMessage(trans('core/base::notices.update_success_message'));
    }
}
