<?php

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use <PERSON><PERSON>qi\FeaturedWebsite\Repositories\Interfaces\FeaturedWebsiteCategoryInterface;
use <PERSON><PERSON>qi\FeaturedWebsite\Repositories\Interfaces\FeaturedWebsiteInterface;


if (! function_exists('get_all_featured_website')) {
    /**
     * @param boolean $active
     * @param int $perPage
     * @param array $with
     * @return Collection
     */
    function get_all_featured_website(
        bool $active = true,
        int $perPage = 12,
        array $with = ['slugable']
    ) {
        return app(FeaturedWebsiteInterface::class)->getAllFeaturedWebsite($perPage, $active, $with);
    }
}

if (! function_exists('get_recent_featured_website')) {
    /**
     * @param int $limit
     * @return Collection
     */
    function get_recent_featured_website(int $limit)
    {
        return app(FeaturedWebsiteInterface::class)->getRecentFeaturedWebsite($limit);
    }
}

if(! function_exists('get_featured_website_by_category')) {
    function get_featured_website_by_category(int|string $categoryId, int $paginate = 12, int $limit = 0): Collection|LengthAwarePaginator
    {
        return app(FeaturedWebsiteInterface::class)->getByCategory($categoryId, $paginate, $limit);
    }
}

// get all categories
if(! function_exists('get_all_featured_website_categories')) {
    function get_all_featured_website_categories(): Collection
    {
        return app(FeaturedWebsiteCategoryInterface::class)->all();
    }
}

