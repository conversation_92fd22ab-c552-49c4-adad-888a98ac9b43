<?php

namespace Shaqi\FeaturedWebsite\Forms;

use <PERSON>haqi\Base\Forms\FormAbstract;
use <PERSON><PERSON>qi\Base\Enums\BaseStatusEnum;
use <PERSON><PERSON>qi\FeaturedWebsite\Http\Requests\FeaturedWebsiteRequest;
use <PERSON><PERSON>qi\FeaturedWebsite\Models\FeaturedWebsite;
use <PERSON><PERSON>qi\FeaturedWebsite\Models\FeaturedWebsiteCategory;
use Shaqi\FeaturedWebsite\Forms\Fields\CategoryMultiField;

class FeaturedWebsiteForm extends FormAbstract
{

    /**
     * {@inheritDoc}
     */
    public function buildForm()
    {
        $selectedCategories = [];
        if ($this->getModel()) {
            $selectedCategories = $this->getModel()->categories()->pluck('featured_website_categories.id')->all();
        }

        if (! $this->formHelper->hasCustomField('categoryMulti')) {
            $this->formHelper->addCustomField('categoryMulti', CategoryMultiField::class);
        }

        $this
            ->setupModel(new FeaturedWebsite)
            ->setValidatorClass(FeaturedWebsiteRequest::class)
            ->withCustomFields()
            ->add('name', 'text', [
                'label'      => trans('core/base::forms.name'),
                'label_attr' => ['class' => 'control-label required'],
                'attr'       => [
                    'placeholder'  => trans('core/base::forms.name_placeholder'),
                    'data-counter' => 120,
                ],
            ])

            ->add('content', 'editor', [
                'label' => trans('core/base::forms.content'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'rows' => 4,
                    'with-short-code' => true,
                ],
            ])
            ->add('status', 'customSelect', [
                'label'      => trans('core/base::tables.status'),
                'label_attr' => ['class' => 'control-label required'],
                'attr'       => [
                    'class' => 'form-control select-full',
                ],
                'choices'    => BaseStatusEnum::labels(),
            ])
            ->add('image', 'mediaImage', [
                'label' => trans('core/base::forms.image'),
                'label_attr' => ['class' => 'control-label'],
            ])
            ->add('categories[]', 'categoryMulti', [
                'label' => 'Categories',
                'label_attr' => ['class' => 'control-label'],
                'choices' => FeaturedWebsiteCategory::where('status', 'published')->pluck('name', 'id')->all(),
                'value' => old('categories', $selectedCategories),
            ])
            ->setBreakFieldPoint('status');
    }
}
