{"__meta": {"id": "Xef858678e4b01a6024f33f303fc505b5", "datetime": "2025-07-31 12:05:32", "utime": 1753963532.982365, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "php": {"version": "8.1.4", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753963517.510229, "end": 1753963532.982439, "duration": 15.472209930419922, "duration_str": "15.47s", "measures": [{"label": "Booting", "start": 1753963517.510229, "relative_start": 0, "end": 1753963520.206871, "relative_end": 1753963520.206871, "duration": 2.6966419219970703, "duration_str": "2.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753963520.208988, "relative_start": 2.698758840560913, "end": 1753963532.982442, "relative_end": 2.86102294921875e-06, "duration": 12.773453950881958, "duration_str": "12.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 54763368, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 38, "templates": [{"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.stats (\\platform\\core\\dashboard\\resources\\views\\widgets\\stats.blade.php)", "param_count": 2, "params": ["widget", "widgetSetting"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/stats.blade.php:0"}, {"name": "core/dashboard::widgets.base (\\platform\\core\\dashboard\\resources\\views\\widgets\\base.blade.php)", "param_count": 4, "params": ["widget", "widgetSetting", "settings", "predefinedRanges"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/base.blade.php:0"}, {"name": "core/dashboard::partials.tools (\\platform\\core\\dashboard\\resources\\views\\partials\\tools.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "widget", "widgetSetting", "settings", "predefinedRanges"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/partials/tools.blade.php:0"}, {"name": "core/dashboard::widgets.base (\\platform\\core\\dashboard\\resources\\views\\widgets\\base.blade.php)", "param_count": 4, "params": ["widget", "widgetSetting", "settings", "predefinedRanges"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/base.blade.php:0"}, {"name": "core/dashboard::partials.tools (\\platform\\core\\dashboard\\resources\\views\\partials\\tools.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "widget", "widgetSetting", "settings", "predefinedRanges"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/partials/tools.blade.php:0"}, {"name": "core/dashboard::widgets.base (\\platform\\core\\dashboard\\resources\\views\\widgets\\base.blade.php)", "param_count": 4, "params": ["widget", "widgetSetting", "settings", "predefinedRanges"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/widgets/base.blade.php:0"}, {"name": "core/dashboard::partials.tools (\\platform\\core\\dashboard\\resources\\views\\partials\\tools.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "widget", "widgetSetting", "settings", "predefinedRanges"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/partials/tools.blade.php:0"}, {"name": "core/dashboard::list (\\platform\\core\\dashboard\\resources\\views\\list.blade.php)", "param_count": 3, "params": ["widgets", "userWidgets", "statWidgets"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/list.blade.php:0"}, {"name": "core/dashboard::partials.modals (\\platform\\core\\dashboard\\resources\\views\\partials\\modals.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/dashboard/resources/views/partials/modals.blade.php:0"}, {"name": "core/base::forms.partials.on-off (\\platform\\core\\base\\resources\\views\\forms\\partials\\on-off.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/on-off.blade.php:0"}, {"name": "core/base::forms.partials.on-off (\\platform\\core\\base\\resources\\views\\forms\\partials\\on-off.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/on-off.blade.php:0"}, {"name": "core/base::forms.partials.on-off (\\platform\\core\\base\\resources\\views\\forms\\partials\\on-off.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/on-off.blade.php:0"}, {"name": "core/base::forms.partials.on-off (\\platform\\core\\base\\resources\\views\\forms\\partials\\on-off.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/on-off.blade.php:0"}, {"name": "core/base::forms.partials.on-off (\\platform\\core\\base\\resources\\views\\forms\\partials\\on-off.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/on-off.blade.php:0"}, {"name": "core/base::forms.partials.on-off (\\platform\\core\\base\\resources\\views\\forms\\partials\\on-off.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/on-off.blade.php:0"}, {"name": "core/base::forms.partials.on-off (\\platform\\core\\base\\resources\\views\\forms\\partials\\on-off.blade.php)", "param_count": 3, "params": ["name", "value", "attributes"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/forms/partials/on-off.blade.php:0"}, {"name": "core/base::layouts.master (\\platform\\core\\base\\resources\\views\\layouts\\master.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/master.blade.php:0"}, {"name": "core/base::layouts.partials.top-header (\\platform\\core\\base\\resources\\views\\layouts\\partials\\top-header.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop", "themes", "activeTheme"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/top-header.blade.php:0"}, {"name": "core/base::layouts.partials.top-menu (\\platform\\core\\base\\resources\\views\\layouts\\partials\\top-menu.blade.php)", "param_count": 11, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop", "themes", "activeTheme"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/top-menu.blade.php:0"}, {"name": "core/base::notification.notification (\\platform\\core\\base\\resources\\views\\notification\\notification.blade.php)", "param_count": 1, "params": ["countNotificationUnread"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/notification/notification.blade.php:0"}, {"name": "plugins/contact::partials.notification (\\platform\\plugins\\contact\\resources\\views\\partials\\notification.blade.php)", "param_count": 1, "params": ["contacts"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/plugins/contact/resources/views/partials/notification.blade.php:0"}, {"name": "core/base::layouts.partials.sidebar (\\platform\\core\\base\\resources\\views\\layouts\\partials\\sidebar.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/sidebar.blade.php:0"}, {"name": "core/base::layouts.partials.breadcrumbs (\\platform\\core\\base\\resources\\views\\layouts\\partials\\breadcrumbs.blade.php)", "param_count": 1, "params": ["breadcrumbs"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.php:0"}, {"name": "core/base::layouts.partials.footer (\\platform\\core\\base\\resources\\views\\layouts\\partials\\footer.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/partials/footer.blade.php:0"}, {"name": "core/media::partials.media (\\platform\\core\\media\\resources\\views\\partials\\media.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/media/resources/views/partials/media.blade.php:0"}, {"name": "core/media::config (\\platform\\core\\media\\resources\\views\\config.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/media/resources/views/config.blade.php:0"}, {"name": "core/base::layouts.base (\\platform\\core\\base\\resources\\views\\layouts\\base.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/layouts/base.blade.php:0"}, {"name": "assets::header (\\vendor\\shaqi\\assets\\resources\\views\\header.blade.php)", "param_count": 2, "params": ["styles", "headScripts"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\assets\\src\\Providers/../../resources/views/header.blade.php:0"}, {"name": "core/base::elements.common (\\platform\\core\\base\\resources\\views\\elements\\common.blade.php)", "param_count": 9, "params": ["__env", "app", "errors", "widgets", "userWidgets", "statWidgets", "__currentLoopData", "widget", "loop"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/elements/common.blade.php:0"}, {"name": "assets::footer (\\vendor\\shaqi\\assets\\resources\\views\\footer.blade.php)", "param_count": 1, "params": ["bodyScripts"], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\assets\\src\\Providers/../../resources/views/footer.blade.php:0"}, {"name": "core/base::notification.notification-content (\\platform\\core\\base\\resources\\views\\notification\\notification-content.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "vscode://file/D:\\laragon\\www\\focusedcre\\platform/core/base/resources/views/notification/notification-content.blade.php:0"}]}, "route": {"uri": "GET admin", "middleware": "web, core, auth", "as": "dashboard.index", "permission": false, "controller": "Shaqi\\Dashboard\\Http\\Controllers\\DashboardController@getDashboard", "namespace": "Shaqi\\Dashboard\\Http\\Controllers", "prefix": "/admin", "where": [], "file": "<a href=\"vscode://file/D:\\laragon\\www\\focusedcre\\vendor\\shaqi\\platform\\dashboard\\src\\Http\\Controllers\\DashboardController.php:25\">\\vendor\\shaqi\\platform\\dashboard\\src\\Http\\Controllers\\DashboardController.php:25-61</a>"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.14189999999999997, "accumulated_duration_str": "142ms", "statements": [{"sql": "select * from `users` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 12}], "duration": 0.00715, "duration_str": "7.15ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "focusedcre", "start_percent": 0, "width_percent": 5.039}, {"sql": "select `id`, `name` from `dashboard_widgets`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\shaqi\\platform\\dashboard\\src\\Http\\Controllers\\DashboardController.php", "line": 46}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.00855, "duration_str": "8.55ms", "stmt_id": "\\vendor\\shaqi\\platform\\dashboard\\src\\Http\\Controllers\\DashboardController.php:46", "connection": "focusedcre", "start_percent": 5.039, "width_percent": 6.025}, {"sql": "select `status`, `order`, `settings`, `widget_id` from `dashboard_widget_settings` where `dashboard_widget_settings`.`widget_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16) and `user_id` = 2 order by `order` asc", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\platform\\dashboard\\src\\Http\\Controllers\\DashboardController.php", "line": 46}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.013529999999999999, "duration_str": "13.53ms", "stmt_id": "\\vendor\\shaqi\\platform\\dashboard\\src\\Http\\Controllers\\DashboardController.php:46", "connection": "focusedcre", "start_percent": 11.064, "width_percent": 9.535}, {"sql": "select count(*) as aggregate from `ec_orders` where `is_finished` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 273}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 160}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 100}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.01877, "duration_str": "18.77ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:273", "connection": "focusedcre", "start_percent": 20.599, "width_percent": 13.228}, {"sql": "select count(*) as aggregate from `ec_products` where `status` = 'published' and `is_variation` = 0", "type": "query", "params": [], "bindings": ["published", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 273}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 160}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 117}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.01293, "duration_str": "12.93ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:273", "connection": "focusedcre", "start_percent": 33.827, "width_percent": 9.112}, {"sql": "select count(*) as aggregate from `ec_customers`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 273}, {"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 160}, {"index": 21, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 133}, {"index": 25, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00872, "duration_str": "8.72ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:273", "connection": "focusedcre", "start_percent": 42.939, "width_percent": 6.145}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 273}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 160}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 148}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00788, "duration_str": "7.88ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:273", "connection": "focusedcre", "start_percent": 49.084, "width_percent": 5.553}, {"sql": "select count(*) as aggregate from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 273}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\acl\\src\\Hooks\\UserWidgetHook.php", "line": 13}, {"index": 22, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:273", "connection": "focusedcre", "start_percent": 54.637, "width_percent": 1.078}, {"sql": "select count(*) as aggregate from `pages` where `status` = 'published'", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 273}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 160}, {"index": 19, "namespace": null, "name": "\\vendor\\shaqi\\page\\src\\Providers\\HookServiceProvider.php", "line": 98}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.011689999999999999, "duration_str": "11.69ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:273", "connection": "focusedcre", "start_percent": 55.715, "width_percent": 8.238}, {"sql": "select `value` from `user_meta` where (`user_id` = 2 and `key` = 'admin-theme') limit 1", "type": "query", "params": [], "bindings": ["2", "admin-theme"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\acl\\src\\Models\\UserMeta.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Providers\\ComposerServiceProvider.php", "line": 24}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 20, "namespace": null, "name": "\\vendor\\shaqi\\shortcode\\src\\View\\View.php", "line": 49}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 147}], "duration": 0.00912, "duration_str": "9.12ms", "stmt_id": "\\vendor\\shaqi\\platform\\acl\\src\\Models\\UserMeta.php:41", "connection": "focusedcre", "start_percent": 63.953, "width_percent": 6.427}, {"sql": "select count(*) as aggregate from `admin_notifications` where `read_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Models\\AdminNotification.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\shaqi\\platform\\base\\src\\Providers\\BaseServiceProvider.php", "line": 199}, {"index": 20, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "duration": 0.00714, "duration_str": "7.14ms", "stmt_id": "\\vendor\\shaqi\\platform\\base\\src\\Models\\AdminNotification.php:52", "connection": "focusedcre", "start_percent": 70.381, "width_percent": 5.032}, {"sql": "select count(*) as aggregate from `contacts` where `status` = 'unread'", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\contact\\src\\Providers\\HookServiceProvider.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.03135, "duration_str": "31.35ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 75.412, "width_percent": 22.093}, {"sql": "select `id`, `name`, `email`, `phone`, `created_at` from `contacts` where `status` = 'unread' order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": ["unread"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\contact\\src\\Providers\\HookServiceProvider.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.0024100000000000002, "duration_str": "2.41ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 97.505, "width_percent": 1.698}, {"sql": "select count(*) as aggregate from `ec_orders` where `status` = 'pending' and `is_finished` = 1", "type": "query", "params": [], "bindings": ["pending", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 367}, {"index": 18, "namespace": null, "name": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Caches\\CacheAbstractDecorator.php", "line": 170}, {"index": 19, "namespace": null, "name": "\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 767}, {"index": 23, "namespace": null, "name": "\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}], "duration": 0.00113, "duration_str": "1.13ms", "stmt_id": "\\vendor\\shaqi\\platform\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php:367", "connection": "focusedcre", "start_percent": 99.204, "width_percent": 0.796}]}, "models": {"data": {"Shaqi\\Contact\\Models\\Contact": 10, "Shaqi\\Dashboard\\Models\\DashboardWidgetSetting": 4, "Shaqi\\Dashboard\\Models\\DashboardWidget": 16, "Shaqi\\ACL\\Models\\User": 1}, "count": 31}, "gate": {"count": 0, "messages": []}, "session": {"_token": "DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "2", "_previous": "array:1 [\n  \"url\" => \"https://focusedcre.local/admin\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "admin-theme": "default", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin", "status_code": "<pre class=sf-dump id=sf-dump-911265804 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-911265804\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1599166743 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1599166743\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1762878898 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1762878898\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-15444259 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">https://focusedcre.local/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2696 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNJYzN3NzIrb28xSjEzVUtrVmNsU3c9PSIsInZhbHVlIjoiNElkQXk2aldDUzVKSW5BcDZHeTNWWXRtejNMeW5FUjFQcUhCSDFpNDJ1RW5oTGY4NnZyQ1lzMDNlK2F1NEpvZXlnbzh5aUJXYXYvUjZTWDJPb0lHc2pxT09IWnFYNHVEZ0pzOUFVRlpGejNBendWSmFvYytkYXdWQVJvM0JPR2MiLCJtYWMiOiIyNDRkZDIzOWIwY2Q1NWMzMDc5MTAyMGUzZGViZDc3YjhlZDIwYWNhYjI0N2Q3M2I0MmJiNjAwMGNlNTNiOTc5IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6InJoU1FOZmYxVTVVRnIxL1JEQStHd2c9PSIsInZhbHVlIjoid2laT1gvWHNqd0c2L3ZVcXRKWm84RVRlUVJ1VTQyV1YwVzBCcXJDSFphck1BTTZvRXJIQ2ptOU9aSVRnVlBqRjRuY1VzYkNpRDFrQkJpd3I5TDhEbmVsVmVaaWNhSkMyZkxOS1ZJbXIwakVrK3ErcjUzSDQ4V2RPRFFwMXdKLzgiLCJtYWMiOiIzOTk2NmFlMzQwZGJlNDBkMmJjNGRjOTg4MmQyOTk4NTQwYjU2N2M5MjhhY2U5MDliNzc1NzIzYTgyOTRlY2EyIiwidGFnIjoiIn0%3D; _gid=GA1.2.**********.**********; _gat_gtag_UA_156191303_1=1; _ga=GA1.1.**********.**********; _ga_94P3KEDHFG=GS2.1.s**********$o13$g0$t1753963517$j57$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15444259\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:49</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>REDIRECT_SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTPS</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>SSL_TLS_SNI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"25 characters\">https://focusedcre.local/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,ur;q=0.8</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"2696 characters\">shaqi_footprints_cookie=eyJpdiI6IkFEcTZ1U2tFTzNDWEdZY0lreEpRUVE9PSIsInZhbHVlIjoiM21qRWJyTWRjZXFHQWN2SVh4OFhyYjdkZTliSHpyMkxPUmZVMjU5ck5wbFIweU5Wb3k1UTBmdkp5M3M1bDlFQzlrVXp6eDcreDJzTjhNdGc2Vy9PcGFmd0RuV3Z6NHQzQ1FSNFF4UEcvSlBvbW9KemRweG1BdnVtdlNPVVN5OTQiLCJtYWMiOiIzNDk1N2Y2MjJhNjZmZTE4ZTBhYTE1OWI4M2EzYjBlNzZjZmU5ZGI4MjRhMmRkYjVmOGVhOTNkMGI0YjdjMjA2IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImdnOFdYUm5iR3pNcENJdVdWc3AxQUE9PSIsInZhbHVlIjoiVWpnS3lqWmtyRFBEbmhNcTN4ZHdsQ1ZMWVp3ME5pMVR3VTNncTRheWNHcTZwUHpRYUZiMjE5SjR0am9CV0ZBUzlHajgzdFVnZmtzOVNobk9HL1R5d3VLdzRBWGo2SVRrcTJuci9xMWx5cWw4cjhKOFR4UzliRVIwS1QyZUxmd2h5TVA1WXhXZnFFVFh5RUcvYXNCSnVOVWV3T0tPdmc1WDl1SFRmckFKSjkrbS94dHdYdVN6bkRiRG5vczBIdTM2N2dKVXM1Qlc3QXB6ZWNUbEFTUStaczdKczZoR1NIb3FLdEE2ZWx3c29QaXgyRlUyK054Wkx2MWdvTDJQSVZVTVQzZTRFWGo5eitabzVGc0lqd1lVT2lmZU52aUZ2eUNqajFTbDFMWnhwQzNQTWUxMmRZVDlIYnpXbzFhSk02QWVWR09xMEFBNk1mbFpuQ1VMWnJCeWU3YVFBdy9EcGdzeEtOMTNVbk5mT2NPN3NXOUFUb1Fqek5xakhQaUdDSFlOL1NrYmVOaFc5dCtXcDNYTFEwQnZzN2lhTW5PdGdMN2VEajNUYnI0UFc2QzczMjdNeEVoK3g1UWFQNmFFc1pUZGVsNStZK1NWdU9xZTJFd2Y3YXZtZVlmYVVMK2d6S2dlbll4dTJxRnhjTE1YbkpwYnhidllVMStOSGxuOW4wR1psOGg3bXcvdk9ZTk1ZMTJEempTQzNBPT0iLCJtYWMiOiI5MmQwMmUyMDE5NTkyYzgxZDY1MTNkYjQwNGY0MjM4YTA4NGExZGY0NTBhNTZhN2UyNDIxYjIyZTAxOGZlY2YwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im93clFaejlBZEVUVXBGckFDWFFFWFE9PSIsInZhbHVlIjoiNkl0dUhaSXJMS2luc1JDU2ZmMTgvZXV0amtsdngrZTIzT093cjlFSlhnWTBtR3pzNGlmM0tMR2kwRkdJTGUrRnNyQ2NiSVdVZHB6cWRDQmM1RlZISC91ZlpBdFlMUnlXTE55cTZrYUQ1c2x5OEVycWpLc3BXTEQydm5jamg3VlFMQWEyUVJuR00zTGlQRHBuVEJpL1cwZHdIRWZkc1V4bGNUTk5hR1o2ZVdqZ0hDcC91dGRwYkZmVlJBcnRsMm4wQmc1Q0dpRzdpQW4wNjFzVEZTQy8yalJVVkk2SXlEeDI5Y2pZVWE1amJNMD0iLCJtYWMiOiIxMDlkNzhjNTU2MTZmNDNjN2ZkYzM0OTg2NjA5NmEyMTRkYmYxZjJkN2ZiN2JhZjdlNDk1ZDU0MWVhNmZiYjc2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlNJYzN3NzIrb28xSjEzVUtrVmNsU3c9PSIsInZhbHVlIjoiNElkQXk2aldDUzVKSW5BcDZHeTNWWXRtejNMeW5FUjFQcUhCSDFpNDJ1RW5oTGY4NnZyQ1lzMDNlK2F1NEpvZXlnbzh5aUJXYXYvUjZTWDJPb0lHc2pxT09IWnFYNHVEZ0pzOUFVRlpGejNBendWSmFvYytkYXdWQVJvM0JPR2MiLCJtYWMiOiIyNDRkZDIzOWIwY2Q1NWMzMDc5MTAyMGUzZGViZDc3YjhlZDIwYWNhYjI0N2Q3M2I0MmJiNjAwMGNlNTNiOTc5IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6InJoU1FOZmYxVTVVRnIxL1JEQStHd2c9PSIsInZhbHVlIjoid2laT1gvWHNqd0c2L3ZVcXRKWm84RVRlUVJ1VTQyV1YwVzBCcXJDSFphck1BTTZvRXJIQ2ptOU9aSVRnVlBqRjRuY1VzYkNpRDFrQkJpd3I5TDhEbmVsVmVaaWNhSkMyZkxOS1ZJbXIwakVrK3ErcjUzSDQ4V2RPRFFwMXdKLzgiLCJtYWMiOiIzOTk2NmFlMzQwZGJlNDBkMmJjNGRjOTg4MmQyOTk4NTQwYjU2N2M5MjhhY2U5MDliNzc1NzIzYTgyOTRlY2EyIiwidGFnIjoiIn0%3D; _gid=GA1.2.**********.**********; _gat_gtag_UA_156191303_1=1; _ga=GA1.1.**********.**********; _ga_94P3KEDHFG=GS2.1.s**********$o13$g0$t1753963517$j57$l0$h0</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1315 characters\">C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\ProgramData\\ComposerSetup\\bin;C:\\Program Files\\Java\\jdk-15.0.2;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;C:\\Users\\<USER>\\.config\\herd\\bin\\nvm;C:\\Program Files\\nodejs;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Amazon\\AWSCLIV2\\;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\laragon\\bin\\composer;D:\\laragon\\bin\\git\\bin;D:\\laragon\\bin\\git\\cmd;D:\\laragon\\bin\\git\\mingw64\\bin;D:\\laragon\\bin\\git\\usr\\bin;D:\\laragon\\bin\\mongodb\\mongodb-4.0.3\\;D:\\laragon\\bin\\mysql\\mariadb-10.6.7-winx64\\bin;D:\\laragon\\bin\\ngrok;D:\\laragon\\bin\\nodejs\\node-v16.16.0;D:\\laragon\\bin\\php\\php-8.1.4-Win32-vs16-x64;D:\\laragon\\bin\\postgresql\\postgresql\\bin;D:\\laragon\\bin\\python\\python-3.13;D:\\laragon\\bin\\python\\python-3.13\\Scripts;D:\\laragon\\bin\\yarn\\bin;D:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"46 characters\">Apache/2.4.47 (Win64) OpenSSL/1.1.1m PHP/8.1.4</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">focusedcre.local</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">443</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"5 characters\">https</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">D:/laragon/www/focusedcre/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"42 characters\">D:/laragon/www/focusedcre/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">23200</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/admin</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/admin</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753963517.5102</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753963517</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1006780477 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">276c8eb96ecbc99bdaa7c37095beb04735960841</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"344 characters\">{&quot;footprint&quot;:&quot;276c8eb96ecbc99bdaa7c37095beb04735960841&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;focusedcre.local&quot;,&quot;landing_page&quot;:&quot;new-website-request&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|6LfPDjUyZLXD0DhNLxRR7WMt2NSfbdUnceBmo2YcfbIcDul5oc1icuy6Y0d2|$2y$10$v3mwe1ya6fDmDwB5vikHTuie26Jjsc.ZMD/k9JtQCyVybjxTgiKCu</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">801BcPCBI6AAQOWuMaIoY4d3XHvjwOixQ6zrnhTO</span>\"\n  \"<span class=sf-dump-key>_gid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_gat_gtag_UA_156191303_1</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_94P3KEDHFG</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006780477\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1288458818 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:05:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im44REZVOXpRN0o1TGFHdGQ3S0xaZEE9PSIsInZhbHVlIjoiTXFYWnF2WGNCSWZqOGZpKzdPWGZUYnZ3UHpxYmZrbDUvUWkzUW1BeVI3dGxIKytLUFNqRWFkMEE0U3JEdUNUa2NlWHBtK0g5c0oycXd3VW9uNXg0eHF2RUVlSmhnbFNwcUlUV29la0xiZkQybFlpeW9yT1NINE1oZ3gva21ZckMiLCJtYWMiOiJkZmMzZDkzNWQ5NGY3ZjRmMGEyMzAxYjVhODdiMmNlZmNmMTRhMzVkZjJhMDYxMzQyOWJlZjdjOWU5OTlmNmRhIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:05:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">shaqi_session=eyJpdiI6IndBalBwaTFwQU55S2c5Z1RaTzVFTHc9PSIsInZhbHVlIjoiaDIwUXVyQ2VzMWFwNnZCNnNWZDQyWlppOFRpZnZ2bTB0OG5PWWhNOUw2TTVMMGZnK1dDdWx1d3RlbGhEL1F1Y2VFb2R6WEpTUHRxZjBybHY4TGl4eVZPc1dRZ2UvUXcxMERqT2pUcnZQd2RScnFkQ05Xa3FGL3V5dElhQWMvZFIiLCJtYWMiOiI5OTI5MjdmYjRjOTRkYTFhYWZkZTc3OTdlNTEyMWNhZWU1Y2MxZDBjMWNhODAwZTZhZDhkYjAyYzg3ODU5Y2Q0IiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 14:05:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im44REZVOXpRN0o1TGFHdGQ3S0xaZEE9PSIsInZhbHVlIjoiTXFYWnF2WGNCSWZqOGZpKzdPWGZUYnZ3UHpxYmZrbDUvUWkzUW1BeVI3dGxIKytLUFNqRWFkMEE0U3JEdUNUa2NlWHBtK0g5c0oycXd3VW9uNXg0eHF2RUVlSmhnbFNwcUlUV29la0xiZkQybFlpeW9yT1NINE1oZ3gva21ZckMiLCJtYWMiOiJkZmMzZDkzNWQ5NGY3ZjRmMGEyMzAxYjVhODdiMmNlZmNmMTRhMzVkZjJhMDYxMzQyOWJlZjdjOWU5OTlmNmRhIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:05:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">shaqi_session=eyJpdiI6IndBalBwaTFwQU55S2c5Z1RaTzVFTHc9PSIsInZhbHVlIjoiaDIwUXVyQ2VzMWFwNnZCNnNWZDQyWlppOFRpZnZ2bTB0OG5PWWhNOUw2TTVMMGZnK1dDdWx1d3RlbGhEL1F1Y2VFb2R6WEpTUHRxZjBybHY4TGl4eVZPc1dRZ2UvUXcxMERqT2pUcnZQd2RScnFkQ05Xa3FGL3V5dElhQWMvZFIiLCJtYWMiOiI5OTI5MjdmYjRjOTRkYTFhYWZkZTc3OTdlNTEyMWNhZWU1Y2MxZDBjMWNhODAwZTZhZDhkYjAyYzg3ODU5Y2Q0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 14:05:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288458818\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1061758979 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DhbuJiYUol2Lc7P8hkHIpPBA1xhtgqoh0enRf7KA</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">https://focusedcre.local/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin-theme</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1061758979\", {\"maxDepth\":0})</script>\n"}}