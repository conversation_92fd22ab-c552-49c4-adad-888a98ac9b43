<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use <PERSON>haqi\FeaturedWebsite\Models\FeaturedWebsite;
use <PERSON>haqi\FeaturedWebsite\Models\FeaturedWebsiteCategory;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Get all featured websites
        $featuredWebsites = FeaturedWebsite::all();
        
        foreach ($featuredWebsites as $website) {
            // Get the website_tag meta data
            $websiteTag = DB::table('meta_boxes')
                ->where('reference_type', FeaturedWebsite::class)
                ->where('reference_id', $website->id)
                ->where('meta_key', 'website_tag')
                ->value('meta_value');
            
            if (!empty($websiteTag)) {
                // Find or create category with this name
                $category = FeaturedWebsiteCategory::firstOrCreate(
                    ['name' => $websiteTag],
                    [
                        'name' => $websiteTag,
                        'status' => 'published'
                    ]
                );
                
                // Associate the website with this category
                $website->categories()->syncWithoutDetaching([$category->id]);
                
                echo "Migrated website '{$website->name}' with tag '{$websiteTag}' to category '{$category->name}'\n";
            }
        }
        
        echo "Migration completed successfully!\n";
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove all category associations (optional - you might want to keep them)
        DB::table('featured_website_category_featured_website')->truncate();
        
        // Optionally delete categories that were created during migration
        // (Be careful with this - only delete if you're sure they were created by this migration)
        echo "Reversed migration - removed category associations\n";
    }
};
